import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { toast } from 'sonner';
import { Mail, TestTube, CheckCircle, XCircle } from 'lucide-react';
import { useAuth } from '../contexts/SupabaseAuthContext';
import { useSupabaseWorkspace } from '../contexts/SupabaseWorkspaceContext';
import { supabase } from '../lib/supabase';

const InvitationTester: React.FC = () => {
  const [testEmail, setTestEmail] = useState('');
  const [testRole, setTestRole] = useState<'member' | 'admin'>('member');
  const [testing, setTesting] = useState(false);
  const [testResults, setTestResults] = useState<string[]>([]);

  const { user } = useAuth();
  const { currentWorkspace, inviteMember } = useSupabaseWorkspace();

  const addTestResult = (result: string, success: boolean = true) => {
    const timestamp = new Date().toLocaleTimeString();
    const icon = success ? '✅' : '❌';
    setTestResults(prev => [...prev, `${icon} [${timestamp}] ${result}`]);
  };

  const testDatabaseConnection = async () => {
    try {
      // First test basic Supabase connection
      const { data: basicTest, error: basicError } = await supabase
        .from('workspaces')
        .select('id')
        .limit(1);

      if (basicError) {
        addTestResult(`Basic Supabase connection failed: ${basicError.message}`, false);
        return false;
      }

      // Then test workspace_invitations table
      const { data, error } = await supabase
        .from('workspace_invitations')
        .select('id')
        .limit(1);

      if (error) {
        if (error.message.includes('relation') && error.message.includes('does not exist')) {
          addTestResult('workspace_invitations table does not exist - run the SQL script first!', false);
        } else {
          addTestResult(`workspace_invitations table error: ${error.message}`, false);
        }
        return false;
      }

      addTestResult('Database connection and table access successful');
      return true;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : JSON.stringify(error);
      addTestResult(`Database connection failed: ${errorMessage}`, false);
      console.error('Database connection error:', error);
      return false;
    }
  };

  const testDatabaseFunctions = async () => {
    try {
      // Test if functions exist by calling them with invalid data
      const { error } = await supabase.rpc('accept_workspace_invitation', {
        invitation_id: '00000000-0000-0000-0000-000000000000',
        user_id: '00000000-0000-0000-0000-000000000000'
      });

      // We expect an error here, but it should be a logical error, not a "function doesn't exist" error
      if (error) {
        if (error.message.includes('function') && error.message.includes('does not exist')) {
          addTestResult('Database functions missing - run the SQL script!', false);
          return false;
        } else if (error.message.includes('User not found') || error.message.includes('Invalid invitation')) {
          addTestResult('Database functions exist and working');
          return true;
        } else {
          addTestResult(`Function test returned: ${error.message}`);
          return true; // Function exists, just returned an expected error
        }
      } else {
        addTestResult('Database functions exist');
        return true;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : JSON.stringify(error);
      addTestResult(`Function test failed: ${errorMessage}`, false);
      return false;
    }
  };

  const testInvitationCreation = async () => {
    if (!currentWorkspace || !testEmail) {
      addTestResult('Missing workspace or email for test', false);
      return false;
    }

    try {
      await inviteMember(currentWorkspace.id, testEmail, testRole);
      addTestResult(`Invitation created for ${testEmail} as ${testRole}`);
      return true;
    } catch (error) {
      addTestResult(`Invitation creation failed: ${error}`, false);
      return false;
    }
  };

  const testInvitationQuery = async () => {
    if (!testEmail) {
      addTestResult('No test email provided', false);
      return false;
    }

    try {
      const { data, error } = await supabase
        .from('workspace_invitations')
        .select('*')
        .eq('invited_email', testEmail)
        .eq('status', 'pending')
        .order('created_at', { ascending: false })
        .limit(1);

      if (error) throw error;

      if (data && data.length > 0) {
        addTestResult(`Found ${data.length} pending invitation(s) for ${testEmail}`);
        return true;
      } else {
        addTestResult(`No pending invitations found for ${testEmail}`, false);
        return false;
      }
    } catch (error) {
      addTestResult(`Invitation query failed: ${error}`, false);
      return false;
    }
  };

  const runFullTest = async () => {
    if (!user || !currentWorkspace) {
      toast.error('Please log in and select a workspace first');
      return;
    }

    if (!testEmail) {
      toast.error('Please enter a test email address');
      return;
    }

    setTesting(true);
    setTestResults([]);

    addTestResult('Starting invitation system test...');

    // Test 1: Database connection
    const dbConnected = await testDatabaseConnection();
    if (!dbConnected) {
      setTesting(false);
      return;
    }

    // Test 2: Database functions
    const functionsExist = await testDatabaseFunctions();
    if (!functionsExist) {
      addTestResult('Please run the INVITATION_SYSTEM_COMPLETE_FIX.sql script', false);
      setTesting(false);
      return;
    }

    // Test 3: Create invitation
    const invitationCreated = await testInvitationCreation();
    
    // Test 4: Query invitation
    if (invitationCreated) {
      await new Promise(resolve => setTimeout(resolve, 1000)); // Wait a bit
      await testInvitationQuery();
    }

    addTestResult('Test completed!');
    setTesting(false);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  if (!user) {
    return (
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            Invitation System Tester
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Please log in to test the invitation system.</p>
        </CardContent>
      </Card>
    );
  }

  if (!currentWorkspace) {
    return (
      <Card className="w-full max-w-2xl">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TestTube className="h-5 w-5" />
            Invitation System Tester
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-muted-foreground">Please select a workspace to test invitations.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-2xl">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TestTube className="h-5 w-5" />
          Invitation System Tester
          <Badge variant="outline">{currentWorkspace.name}</Badge>
        </CardTitle>
        <CardDescription>
          Test the invitation system functionality
        </CardDescription>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="test-email">Test Email</Label>
            <Input
              id="test-email"
              type="email"
              placeholder="<EMAIL>"
              value={testEmail}
              onChange={(e) => setTestEmail(e.target.value)}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="test-role">Role</Label>
            <Select value={testRole} onValueChange={(value: 'member' | 'admin') => setTestRole(value)}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="member">Member</SelectItem>
                <SelectItem value="admin">Admin</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <div className="flex gap-2">
          <Button 
            onClick={runFullTest} 
            disabled={testing || !testEmail}
            className="flex-1"
          >
            <Mail className="h-4 w-4 mr-2" />
            {testing ? 'Testing...' : 'Run Full Test'}
          </Button>
          
          <Button 
            variant="outline" 
            onClick={clearResults}
            disabled={testing}
          >
            Clear
          </Button>
        </div>

        {testResults.length > 0 && (
          <div className="space-y-2">
            <Label>Test Results:</Label>
            <div className="bg-muted p-3 rounded-md max-h-60 overflow-y-auto">
              <div className="space-y-1 font-mono text-sm">
                {testResults.map((result, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <span className="text-muted-foreground">{index + 1}.</span>
                    <span>{result}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        <div className="text-sm text-muted-foreground">
          <p><strong>Note:</strong> Make sure you've run the database setup script first.</p>
          <p>This test will create a real invitation record in your database.</p>
        </div>
      </CardContent>
    </Card>
  );
};

export default InvitationTester;
