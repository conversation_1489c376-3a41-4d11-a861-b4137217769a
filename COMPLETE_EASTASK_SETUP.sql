-- =====================================================
-- COMPLETE EASTASK DATABASE SETUP SCRIPT
-- =====================================================
-- This script sets up ALL features for EasTask including:
-- ✅ User Profiles & Settings
-- ✅ Advanced Task Features (Subtasks, Dependencies, Comments)
-- ✅ Time Tracking
-- ✅ File Attachments
-- ✅ Real-time Collaboration
-- ✅ Analytics & Activity Tracking

-- =====================================================
-- STEP 1: BASIC TASK MANAGEMENT TABLES
-- =====================================================

-- Workspaces table (if not exists)
CREATE TABLE IF NOT EXISTS workspaces (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL CHECK (length(name) > 0),
  description TEXT,
  owner_id UUID NOT NULL REFERENCES auth.users(id),
  settings JSONB DEFAULT '{}',
  is_public BOOLEAN DEFAULT false,
  invite_code TEXT UNIQUE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Workspace members table
CREATE TABLE IF NOT EXISTS workspace_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id UUID NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  role TEXT NOT NULL DEFAULT 'member' CHECK (role IN ('owner', 'admin', 'member', 'guest')),
  joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  invited_by UUID REFERENCES auth.users(id),
  UNIQUE(workspace_id, user_id)
);

-- Pages table
CREATE TABLE IF NOT EXISTS pages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id UUID NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
  title TEXT NOT NULL CHECK (length(title) > 0),
  description TEXT,
  url TEXT,
  created_by UUID NOT NULL REFERENCES auth.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tasks table (enhanced)
CREATE TABLE IF NOT EXISTS tasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id UUID NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
  page_id UUID REFERENCES pages(id) ON DELETE SET NULL,
  title TEXT NOT NULL CHECK (length(title) > 0),
  description TEXT,
  status TEXT NOT NULL DEFAULT 'todo' CHECK (status IN ('todo', 'progress', 'done')),
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  due_date TIMESTAMP WITH TIME ZONE,
  start_date TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  assigned_to UUID REFERENCES auth.users(id),
  created_by UUID NOT NULL REFERENCES auth.users(id),
  tags TEXT[] DEFAULT '{}',
  link TEXT,
  order_index INTEGER NOT NULL DEFAULT 0,
  estimated_hours DECIMAL(5,2),
  actual_hours DECIMAL(5,2),
  progress INTEGER DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- STEP 2: USER PROFILES & SETTINGS
-- =====================================================

-- User profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  full_name TEXT,
  phone TEXT,
  location TEXT,
  job_title TEXT,
  department TEXT,
  employee_id TEXT,
  bio TEXT,
  timezone TEXT DEFAULT 'UTC',
  pronouns TEXT,
  start_date DATE,
  manager TEXT,
  skills TEXT[] DEFAULT '{}',
  profile_image_url TEXT,
  working_hours_start TIME DEFAULT '09:00:00',
  working_hours_end TIME DEFAULT '17:00:00',
  preferred_communication TEXT DEFAULT 'email',
  goals_objectives TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id),
  UNIQUE(employee_id)
);

-- User settings table
CREATE TABLE IF NOT EXISTS user_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Appearance Settings
  theme TEXT DEFAULT 'system' CHECK (theme IN ('light', 'dark', 'system')),
  language TEXT DEFAULT 'en' CHECK (language IN ('en', 'es', 'fr', 'de', 'ja')),
  timezone TEXT DEFAULT 'UTC',
  date_format TEXT DEFAULT 'mm/dd/yyyy' CHECK (date_format IN ('mm/dd/yyyy', 'dd/mm/yyyy', 'yyyy-mm-dd')),
  time_format TEXT DEFAULT '12' CHECK (time_format IN ('12', '24')),
  first_day_of_week TEXT DEFAULT 'sunday' CHECK (first_day_of_week IN ('sunday', 'monday')),
  currency TEXT DEFAULT 'usd' CHECK (currency IN ('usd', 'eur', 'gbp', 'inr')),
  
  -- Notification Settings
  email_notifications BOOLEAN DEFAULT true,
  push_notifications BOOLEAN DEFAULT true,
  task_reminders BOOLEAN DEFAULT true,
  project_updates BOOLEAN DEFAULT true,
  weekly_digest BOOLEAN DEFAULT false,
  marketing_emails BOOLEAN DEFAULT false,
  
  -- Privacy Settings
  profile_visibility TEXT DEFAULT 'team' CHECK (profile_visibility IN ('public', 'team', 'private')),
  activity_status BOOLEAN DEFAULT true,
  data_sharing BOOLEAN DEFAULT false,
  analytics_tracking BOOLEAN DEFAULT true,
  
  -- Security Settings
  two_factor_auth BOOLEAN DEFAULT false,
  session_timeout INTEGER DEFAULT 30,
  login_alerts BOOLEAN DEFAULT true,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- =====================================================
-- STEP 3: ADVANCED TASK FEATURES
-- =====================================================

-- Task dependencies table
CREATE TABLE IF NOT EXISTS task_dependencies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  depends_on_task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  dependency_type TEXT NOT NULL DEFAULT 'finish_to_start' CHECK (
    dependency_type IN ('finish_to_start', 'start_to_start', 'finish_to_finish', 'start_to_finish')
  ),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID NOT NULL REFERENCES auth.users(id),
  UNIQUE(task_id, depends_on_task_id),
  CHECK (task_id != depends_on_task_id)
);

-- Subtasks table
CREATE TABLE IF NOT EXISTS subtasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  parent_task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  title TEXT NOT NULL CHECK (length(title) > 0),
  description TEXT,
  status TEXT NOT NULL DEFAULT 'todo' CHECK (status IN ('todo', 'progress', 'done')),
  assigned_to UUID REFERENCES auth.users(id),
  due_date TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  order_index INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID NOT NULL REFERENCES auth.users(id)
);

-- Task comments table
CREATE TABLE IF NOT EXISTS task_comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id),
  content TEXT NOT NULL CHECK (length(content) > 0),
  parent_comment_id UUID REFERENCES task_comments(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_edited BOOLEAN DEFAULT FALSE
);

-- Task time entries table
CREATE TABLE IF NOT EXISTS task_time_entries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id),
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE,
  duration_minutes INTEGER,
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Task assignments table (for multiple assignees)
CREATE TABLE IF NOT EXISTS task_assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id),
  assigned_by UUID NOT NULL REFERENCES auth.users(id),
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  role TEXT DEFAULT 'assignee' CHECK (role IN ('assignee', 'reviewer', 'watcher')),
  UNIQUE(task_id, user_id, role)
);

-- Task attachments table
CREATE TABLE IF NOT EXISTS task_attachments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  comment_id UUID REFERENCES task_comments(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id),
  file_name TEXT NOT NULL,
  file_size INTEGER NOT NULL,
  file_type TEXT NOT NULL,
  file_url TEXT NOT NULL,
  storage_path TEXT NOT NULL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- STEP 4: ACTIVITY TRACKING & ANALYTICS
-- =====================================================

-- User activity table
CREATE TABLE IF NOT EXISTS user_activity (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id),
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  activity_type TEXT NOT NULL,
  activity_description TEXT,
  metadata JSONB DEFAULT '{}',
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User statistics table
CREATE TABLE IF NOT EXISTS user_statistics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id),
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  date DATE NOT NULL DEFAULT CURRENT_DATE,
  tasks_created INTEGER DEFAULT 0,
  tasks_completed INTEGER DEFAULT 0,
  time_tracked_minutes INTEGER DEFAULT 0,
  comments_posted INTEGER DEFAULT 0,
  files_uploaded INTEGER DEFAULT 0,
  login_count INTEGER DEFAULT 0,
  last_active_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, workspace_id, date)
);

-- =====================================================
-- STEP 5: FUNCTIONS & TRIGGERS
-- =====================================================

-- Function to check circular dependencies
DROP FUNCTION IF EXISTS check_circular_dependency(UUID, UUID);
CREATE OR REPLACE FUNCTION check_circular_dependency(p_task_id UUID, p_depends_on_task_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  circular_found BOOLEAN := FALSE;
BEGIN
  -- Use recursive CTE to check for circular dependencies
  WITH RECURSIVE dependency_chain AS (
    -- Base case: direct dependency
    SELECT depends_on_task_id as current_task, 1 as depth
    FROM task_dependencies
    WHERE task_id = p_depends_on_task_id

    UNION ALL

    -- Recursive case: follow the chain
    SELECT td.depends_on_task_id, dc.depth + 1
    FROM task_dependencies td
    JOIN dependency_chain dc ON td.task_id = dc.current_task
    WHERE dc.depth < 10 -- Prevent infinite loops
  )
  SELECT EXISTS(
    SELECT 1 FROM dependency_chain WHERE current_task = p_task_id
  ) INTO circular_found;

  RETURN circular_found;
END;
$$ LANGUAGE plpgsql;

-- Function to calculate task progress based on subtasks
DROP FUNCTION IF EXISTS calculate_task_progress(UUID);
CREATE OR REPLACE FUNCTION calculate_task_progress(p_task_id UUID)
RETURNS INTEGER AS $$
DECLARE
  total_subtasks INTEGER;
  completed_subtasks INTEGER;
  progress_percentage INTEGER;
BEGIN
  -- Count total and completed subtasks
  SELECT COUNT(*) INTO total_subtasks
  FROM subtasks WHERE parent_task_id = p_task_id;

  SELECT COUNT(*) INTO completed_subtasks
  FROM subtasks WHERE parent_task_id = p_task_id AND status = 'done';

  -- Calculate progress percentage
  IF total_subtasks = 0 THEN
    progress_percentage := 0;
  ELSE
    progress_percentage := ROUND((completed_subtasks::DECIMAL / total_subtasks::DECIMAL) * 100);
  END IF;

  RETURN progress_percentage;
END;
$$ LANGUAGE plpgsql;

-- Function to update task statistics
CREATE OR REPLACE FUNCTION update_task_statistics()
RETURNS TRIGGER AS $$
BEGIN
  -- Update task progress when subtasks change
  IF TG_TABLE_NAME = 'subtasks' THEN
    UPDATE tasks
    SET progress = calculate_task_progress(NEW.parent_task_id),
        updated_at = NOW()
    WHERE id = NEW.parent_task_id;
  END IF;

  -- Update actual hours when time entries change
  IF TG_TABLE_NAME = 'task_time_entries' THEN
    UPDATE tasks
    SET actual_hours = (
      SELECT COALESCE(SUM(duration_minutes), 0) / 60.0
      FROM task_time_entries
      WHERE task_id = NEW.task_id
    ),
    updated_at = NOW()
    WHERE id = NEW.task_id;
  END IF;

  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to log user activity
CREATE OR REPLACE FUNCTION log_user_activity(
  p_user_id UUID,
  p_activity_type TEXT,
  p_activity_description TEXT DEFAULT NULL,
  p_metadata JSONB DEFAULT '{}'::jsonb
)
RETURNS UUID AS $$
DECLARE
  activity_id UUID;
BEGIN
  INSERT INTO user_activity (
    user_id,
    activity_type,
    activity_description,
    metadata
  ) VALUES (
    p_user_id,
    p_activity_type,
    p_activity_description,
    p_metadata
  ) RETURNING id INTO activity_id;

  RETURN activity_id;
END;
$$ LANGUAGE plpgsql;

-- =====================================================
-- STEP 6: TRIGGERS
-- =====================================================

-- Trigger to prevent circular dependencies
CREATE OR REPLACE FUNCTION prevent_circular_dependency()
RETURNS TRIGGER AS $$
BEGIN
  IF check_circular_dependency(NEW.task_id, NEW.depends_on_task_id) THEN
    RAISE EXCEPTION 'Circular dependency detected between tasks % and %', NEW.task_id, NEW.depends_on_task_id;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

DROP TRIGGER IF EXISTS prevent_circular_dependency ON task_dependencies;
CREATE TRIGGER prevent_circular_dependency
  BEFORE INSERT OR UPDATE ON task_dependencies
  FOR EACH ROW EXECUTE FUNCTION prevent_circular_dependency();

-- Trigger to update task statistics
DROP TRIGGER IF EXISTS update_task_statistics_subtasks ON subtasks;
CREATE TRIGGER update_task_statistics_subtasks
  AFTER INSERT OR UPDATE OR DELETE ON subtasks
  FOR EACH ROW EXECUTE FUNCTION update_task_statistics();

DROP TRIGGER IF EXISTS update_task_statistics_time ON task_time_entries;
CREATE TRIGGER update_task_statistics_time
  AFTER INSERT OR UPDATE OR DELETE ON task_time_entries
  FOR EACH ROW EXECUTE FUNCTION update_task_statistics();

-- Trigger to update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply update timestamp triggers to all relevant tables
DROP TRIGGER IF EXISTS update_workspaces_updated_at ON workspaces;
CREATE TRIGGER update_workspaces_updated_at
  BEFORE UPDATE ON workspaces
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_pages_updated_at ON pages;
CREATE TRIGGER update_pages_updated_at
  BEFORE UPDATE ON pages
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_tasks_updated_at ON tasks;
CREATE TRIGGER update_tasks_updated_at
  BEFORE UPDATE ON tasks
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_profiles_updated_at ON user_profiles;
CREATE TRIGGER update_user_profiles_updated_at
  BEFORE UPDATE ON user_profiles
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_settings_updated_at ON user_settings;
CREATE TRIGGER update_user_settings_updated_at
  BEFORE UPDATE ON user_settings
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_subtasks_updated_at ON subtasks;
CREATE TRIGGER update_subtasks_updated_at
  BEFORE UPDATE ON subtasks
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_task_comments_updated_at ON task_comments;
CREATE TRIGGER update_task_comments_updated_at
  BEFORE UPDATE ON task_comments
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_task_time_entries_updated_at ON task_time_entries;
CREATE TRIGGER update_task_time_entries_updated_at
  BEFORE UPDATE ON task_time_entries
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_statistics_updated_at ON user_statistics;
CREATE TRIGGER update_user_statistics_updated_at
  BEFORE UPDATE ON user_statistics
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- STEP 7: ROW LEVEL SECURITY (RLS) POLICIES
-- =====================================================

-- Enable RLS on all tables
ALTER TABLE workspaces ENABLE ROW LEVEL SECURITY;
ALTER TABLE workspace_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE pages ENABLE ROW LEVEL SECURITY;
ALTER TABLE tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_dependencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE subtasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_time_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_attachments ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_activity ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_statistics ENABLE ROW LEVEL SECURITY;

-- Workspace policies
CREATE POLICY "Users can view workspaces they are members of" ON workspaces
  FOR SELECT USING (
    id IN (
      SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()
    )
  );

CREATE POLICY "Users can create workspaces" ON workspaces
  FOR INSERT WITH CHECK (owner_id = auth.uid());

CREATE POLICY "Workspace owners can update their workspaces" ON workspaces
  FOR UPDATE USING (owner_id = auth.uid());

-- Workspace members policies
CREATE POLICY "Users can view workspace members in their workspaces" ON workspace_members
  FOR SELECT USING (
    workspace_id IN (
      SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()
    )
  );

-- Pages policies
CREATE POLICY "Users can manage pages in their workspaces" ON pages
  FOR ALL USING (
    workspace_id IN (
      SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()
    )
  );

-- Tasks policies
CREATE POLICY "Users can manage tasks in their workspaces" ON tasks
  FOR ALL USING (
    workspace_id IN (
      SELECT workspace_id FROM workspace_members WHERE user_id = auth.uid()
    )
  );

-- User profiles policies
CREATE POLICY "Users can view profiles in their workspaces" ON user_profiles
  FOR SELECT USING (
    user_id IN (
      SELECT DISTINCT wm2.user_id
      FROM workspace_members wm1
      JOIN workspace_members wm2 ON wm1.workspace_id = wm2.workspace_id
      WHERE wm1.user_id = auth.uid()
    ) OR user_id = auth.uid()
  );

CREATE POLICY "Users can manage their own profile" ON user_profiles
  FOR ALL USING (user_id = auth.uid());

-- User settings policies
CREATE POLICY "Users can manage their own settings" ON user_settings
  FOR ALL USING (user_id = auth.uid());

-- Advanced task features policies
CREATE POLICY "Users can manage subtasks in their workspaces" ON subtasks
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM tasks t
      JOIN workspace_members wm ON t.workspace_id = wm.workspace_id
      WHERE t.id = subtasks.parent_task_id AND wm.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage dependencies in their workspaces" ON task_dependencies
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM tasks t
      JOIN workspace_members wm ON t.workspace_id = wm.workspace_id
      WHERE t.id = task_dependencies.task_id AND wm.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage comments in their workspaces" ON task_comments
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM tasks t
      JOIN workspace_members wm ON t.workspace_id = wm.workspace_id
      WHERE t.id = task_comments.task_id AND wm.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage time entries in their workspaces" ON task_time_entries
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM tasks t
      JOIN workspace_members wm ON t.workspace_id = wm.workspace_id
      WHERE t.id = task_time_entries.task_id AND wm.user_id = auth.uid()
    )
  );

-- =====================================================
-- STEP 8: STORAGE SETUP
-- =====================================================

-- Create storage buckets
INSERT INTO storage.buckets (id, name, public)
VALUES ('avatars', 'avatars', true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.buckets (id, name, public)
VALUES ('task-attachments', 'task-attachments', false)
ON CONFLICT (id) DO NOTHING;

-- Storage policies for avatars
CREATE POLICY "Avatar images are publicly accessible" ON storage.objects
  FOR SELECT USING (bucket_id = 'avatars');

CREATE POLICY "Users can upload their own avatar" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'avatars' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update their own avatar" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'avatars' AND
    auth.uid()::text = (storage.foldername(name))[1]
  );

-- Storage policies for task attachments
CREATE POLICY "Users can view attachments in their workspaces" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'task-attachments' AND
    EXISTS (
      SELECT 1 FROM task_attachments ta
      JOIN tasks t ON ta.task_id = t.id
      JOIN workspace_members wm ON t.workspace_id = wm.workspace_id
      WHERE ta.storage_path = name AND wm.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can upload attachments to their workspace tasks" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'task-attachments' AND
    auth.uid() IS NOT NULL
  );

-- =====================================================
-- STEP 9: GRANT PERMISSIONS
-- =====================================================

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION check_circular_dependency(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_task_progress(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION log_user_activity(UUID, TEXT, TEXT, JSONB) TO authenticated;

-- =====================================================
-- STEP 10: SUCCESS MESSAGE
-- =====================================================

DO $$
BEGIN
  RAISE NOTICE '🎉 EasTask Complete Database Setup Successful! 🎉';
  RAISE NOTICE '';
  RAISE NOTICE '✅ Tables Created:';
  RAISE NOTICE '   - workspaces, workspace_members, pages, tasks';
  RAISE NOTICE '   - user_profiles, user_settings';
  RAISE NOTICE '   - task_dependencies, subtasks, task_comments';
  RAISE NOTICE '   - task_time_entries, task_assignments, task_attachments';
  RAISE NOTICE '   - user_activity, user_statistics';
  RAISE NOTICE '';
  RAISE NOTICE '✅ Features Enabled:';
  RAISE NOTICE '   - 🏢 Multi-workspace collaboration';
  RAISE NOTICE '   - 👤 User profiles & settings';
  RAISE NOTICE '   - 📋 Advanced task management';
  RAISE NOTICE '   - 🔗 Task dependencies & subtasks';
  RAISE NOTICE '   - 💬 Task comments & discussions';
  RAISE NOTICE '   - ⏱️ Time tracking & reporting';
  RAISE NOTICE '   - 📎 File attachments';
  RAISE NOTICE '   - 📊 Activity tracking & analytics';
  RAISE NOTICE '   - 🔒 Row-level security';
  RAISE NOTICE '   - 📁 File storage with policies';
  RAISE NOTICE '';
  RAISE NOTICE '🚀 Your EasTask application is now ready for production!';
END $$;
