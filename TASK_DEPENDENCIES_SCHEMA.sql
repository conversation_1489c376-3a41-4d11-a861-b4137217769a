-- =====================================================
-- EASTAK TASK DEPENDENCIES & ADVANCED FEATURES SCHEMA
-- =====================================================
-- This script adds advanced task management features

-- STEP 1: Add task dependencies table
CREATE TABLE IF NOT EXISTS task_dependencies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  depends_on_task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  dependency_type TEXT NOT NULL DEFAULT 'finish_to_start' CHECK (
    dependency_type IN ('finish_to_start', 'start_to_start', 'finish_to_finish', 'start_to_finish')
  ),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID NOT NULL REFERENCES auth.users(id),
  UNIQUE(task_id, depends_on_task_id),
  CHECK (task_id != depends_on_task_id) -- Prevent self-dependency
);

-- STEP 2: Add subtasks table
CREATE TABLE IF NOT EXISTS subtasks (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  parent_task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  title TEXT NOT NULL CHECK (length(title) > 0),
  description TEXT,
  status TEXT NOT NULL DEFAULT 'todo' CHECK (status IN ('todo', 'progress', 'done')),
  assigned_to UUID REFERENCES auth.users(id),
  due_date TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  order_index INTEGER NOT NULL DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID NOT NULL REFERENCES auth.users(id)
);

-- STEP 3: Add task comments table
CREATE TABLE IF NOT EXISTS task_comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id),
  content TEXT NOT NULL CHECK (length(content) > 0),
  parent_comment_id UUID REFERENCES task_comments(id) ON DELETE CASCADE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  is_edited BOOLEAN DEFAULT FALSE
);

-- STEP 4: Add task time tracking table
CREATE TABLE IF NOT EXISTS task_time_entries (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id),
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE,
  duration_minutes INTEGER, -- Calculated field
  description TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- STEP 5: Add task assignments table (for multiple assignees)
CREATE TABLE IF NOT EXISTS task_assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES auth.users(id),
  assigned_by UUID NOT NULL REFERENCES auth.users(id),
  assigned_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  role TEXT DEFAULT 'assignee' CHECK (role IN ('assignee', 'reviewer', 'watcher')),
  UNIQUE(task_id, user_id, role)
);

-- STEP 6: Add task attachments table
CREATE TABLE IF NOT EXISTS task_attachments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  task_id UUID NOT NULL REFERENCES tasks(id) ON DELETE CASCADE,
  file_name TEXT NOT NULL,
  file_size INTEGER,
  file_type TEXT,
  storage_path TEXT NOT NULL,
  uploaded_by UUID NOT NULL REFERENCES auth.users(id),
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- STEP 7: Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_task_dependencies_task_id ON task_dependencies(task_id);
CREATE INDEX IF NOT EXISTS idx_task_dependencies_depends_on ON task_dependencies(depends_on_task_id);
CREATE INDEX IF NOT EXISTS idx_subtasks_parent_task ON subtasks(parent_task_id);
CREATE INDEX IF NOT EXISTS idx_subtasks_assigned_to ON subtasks(assigned_to);
CREATE INDEX IF NOT EXISTS idx_task_comments_task_id ON task_comments(task_id);
CREATE INDEX IF NOT EXISTS idx_task_comments_user_id ON task_comments(user_id);
CREATE INDEX IF NOT EXISTS idx_task_time_entries_task_id ON task_time_entries(task_id);
CREATE INDEX IF NOT EXISTS idx_task_time_entries_user_id ON task_time_entries(user_id);
CREATE INDEX IF NOT EXISTS idx_task_assignments_task_id ON task_assignments(task_id);
CREATE INDEX IF NOT EXISTS idx_task_assignments_user_id ON task_assignments(user_id);
CREATE INDEX IF NOT EXISTS idx_task_attachments_task_id ON task_attachments(task_id);

-- STEP 8: Create functions for task dependency validation
CREATE OR REPLACE FUNCTION check_circular_dependency(
  p_task_id UUID,
  p_depends_on_task_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
  circular_found BOOLEAN := FALSE;
BEGIN
  -- Use recursive CTE to check for circular dependencies
  WITH RECURSIVE dependency_chain AS (
    -- Base case: direct dependency
    SELECT depends_on_task_id as task_id, 1 as depth
    FROM task_dependencies 
    WHERE task_id = p_depends_on_task_id
    
    UNION ALL
    
    -- Recursive case: follow the chain
    SELECT td.depends_on_task_id, dc.depth + 1
    FROM task_dependencies td
    JOIN dependency_chain dc ON td.task_id = dc.task_id
    WHERE dc.depth < 10 -- Prevent infinite recursion
  )
  SELECT EXISTS(
    SELECT 1 FROM dependency_chain WHERE task_id = p_task_id
  ) INTO circular_found;
  
  RETURN circular_found;
END;
$$ LANGUAGE plpgsql;

-- STEP 9: Create trigger to prevent circular dependencies
CREATE OR REPLACE FUNCTION prevent_circular_dependency()
RETURNS TRIGGER AS $$
BEGIN
  IF check_circular_dependency(NEW.task_id, NEW.depends_on_task_id) THEN
    RAISE EXCEPTION 'Circular dependency detected. Task % cannot depend on task % as it would create a cycle.', 
      NEW.task_id, NEW.depends_on_task_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_prevent_circular_dependency
  BEFORE INSERT OR UPDATE ON task_dependencies
  FOR EACH ROW EXECUTE FUNCTION prevent_circular_dependency();

-- STEP 10: Create function to calculate task progress based on subtasks
CREATE OR REPLACE FUNCTION calculate_task_progress(p_task_id UUID)
RETURNS INTEGER AS $$
DECLARE
  total_subtasks INTEGER;
  completed_subtasks INTEGER;
  progress_percentage INTEGER;
BEGIN
  -- Count total and completed subtasks
  SELECT 
    COUNT(*),
    COUNT(*) FILTER (WHERE status = 'done')
  INTO total_subtasks, completed_subtasks
  FROM subtasks 
  WHERE parent_task_id = p_task_id;
  
  -- If no subtasks, return 0
  IF total_subtasks = 0 THEN
    RETURN 0;
  END IF;
  
  -- Calculate percentage
  progress_percentage := ROUND((completed_subtasks::DECIMAL / total_subtasks::DECIMAL) * 100);
  
  RETURN progress_percentage;
END;
$$ LANGUAGE plpgsql;

-- STEP 11: Create function to update task statistics
CREATE OR REPLACE FUNCTION update_task_statistics()
RETURNS TRIGGER AS $$
BEGIN
  -- Update user statistics when tasks are completed
  IF NEW.status = 'done' AND (OLD.status IS NULL OR OLD.status != 'done') THEN
    -- Task was just completed
    INSERT INTO user_statistics (user_id, tasks_completed, last_calculated, updated_at)
    VALUES (NEW.assigned_to, 1, NOW(), NOW())
    ON CONFLICT (user_id) 
    DO UPDATE SET
      tasks_completed = user_statistics.tasks_completed + 1,
      last_calculated = NOW(),
      updated_at = NOW();
      
    -- Log activity
    INSERT INTO user_activity (user_id, activity_type, activity_description, metadata)
    VALUES (
      NEW.assigned_to,
      'task_completed',
      'Completed task: ' || NEW.title,
      jsonb_build_object('task_id', NEW.id, 'task_title', NEW.title)
    );
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_task_statistics
  AFTER UPDATE ON tasks
  FOR EACH ROW EXECUTE FUNCTION update_task_statistics();

-- STEP 12: Set up RLS policies
ALTER TABLE task_dependencies ENABLE ROW LEVEL SECURITY;
ALTER TABLE subtasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_comments ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_time_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE task_attachments ENABLE ROW LEVEL SECURITY;

-- Policies for task_dependencies
CREATE POLICY "Users can view dependencies in their workspaces" ON task_dependencies
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM tasks t
      JOIN workspace_members wm ON t.workspace_id = wm.workspace_id
      WHERE t.id = task_dependencies.task_id AND wm.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage dependencies in their workspaces" ON task_dependencies
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM tasks t
      JOIN workspace_members wm ON t.workspace_id = wm.workspace_id
      WHERE t.id = task_dependencies.task_id AND wm.user_id = auth.uid()
    )
  );

-- Policies for subtasks
CREATE POLICY "Users can view subtasks in their workspaces" ON subtasks
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM tasks t
      JOIN workspace_members wm ON t.workspace_id = wm.workspace_id
      WHERE t.id = subtasks.parent_task_id AND wm.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage subtasks in their workspaces" ON subtasks
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM tasks t
      JOIN workspace_members wm ON t.workspace_id = wm.workspace_id
      WHERE t.id = subtasks.parent_task_id AND wm.user_id = auth.uid()
    )
  );

-- Policies for task_comments
CREATE POLICY "Users can view comments in their workspaces" ON task_comments
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM tasks t
      JOIN workspace_members wm ON t.workspace_id = wm.workspace_id
      WHERE t.id = task_comments.task_id AND wm.user_id = auth.uid()
    )
  );

CREATE POLICY "Users can manage their own comments" ON task_comments
  FOR ALL USING (user_id = auth.uid());

-- Grant permissions
GRANT EXECUTE ON FUNCTION check_circular_dependency(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION calculate_task_progress(UUID) TO authenticated;

-- Success message
DO $$
BEGIN
  RAISE NOTICE 'EasTask Advanced Task Features schema created successfully!';
  RAISE NOTICE 'Tables created: task_dependencies, subtasks, task_comments, task_time_entries, task_assignments, task_attachments';
  RAISE NOTICE 'Functions created: check_circular_dependency, calculate_task_progress, update_task_statistics';
  RAISE NOTICE 'Triggers created: prevent_circular_dependency, update_task_statistics';
  RAISE NOTICE 'RLS policies created for security';
END $$;
