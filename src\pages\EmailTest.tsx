import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import EmailServiceTest from '../components/EmailServiceTest';
import DatabaseTest from '../components/DatabaseTest';
import ComprehensiveEmailTest from '../components/ComprehensiveEmailTest';
import ModernLayout from '../components/ModernLayout';
import { Mail, TestTube, Settings, CheckCircle, AlertCircle } from 'lucide-react';

const EmailTest: React.FC = () => {
  const emailConfig = {
    provider: import.meta.env.VITE_EMAIL_SERVICE_PROVIDER || 'demo',
    apiKey: !!import.meta.env.VITE_EMAIL_SERVICE_API_KEY,
    fromEmail: import.meta.env.VITE_FROM_EMAIL || '<EMAIL>',
    fromName: import.meta.env.VITE_FROM_NAME || 'EasTask Team',
    appUrl: import.meta.env.VITE_APP_URL || 'http://localhost:8081'
  };

  const getProviderStatus = () => {
    if (emailConfig.provider === 'demo') {
      return { status: 'demo', color: 'secondary', icon: TestTube };
    } else if (emailConfig.apiKey) {
      return { status: 'configured', color: 'default', icon: CheckCircle };
    } else {
      return { status: 'missing-key', color: 'destructive', icon: AlertCircle };
    }
  };

  const providerStatus = getProviderStatus();
  const StatusIcon = providerStatus.icon;

  return (
    <ModernLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Email Service Testing</h1>
            <p className="text-muted-foreground">
              Test and configure email notifications for EasTask
            </p>
          </div>
          <Badge variant={providerStatus.color as any} className="flex items-center gap-2">
            <StatusIcon className="h-4 w-4" />
            {providerStatus.status === 'demo' && 'Demo Mode'}
            {providerStatus.status === 'configured' && 'Configured'}
            {providerStatus.status === 'missing-key' && 'Missing API Key'}
          </Badge>
        </div>

        {/* Configuration Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Email Configuration Overview
            </CardTitle>
            <CardDescription>
              Current email service configuration and status
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="space-y-2">
                <div className="text-sm font-medium">Provider</div>
                <div className="text-2xl font-bold capitalize">{emailConfig.provider}</div>
                <div className="text-xs text-muted-foreground">
                  {emailConfig.provider === 'resend' && 'Resend Email Service'}
                  {emailConfig.provider === 'sendgrid' && 'SendGrid Email Service'}
                  {emailConfig.provider === 'demo' && 'Demo Mode (Console Only)'}
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="text-sm font-medium">API Key</div>
                <div className="text-2xl font-bold">
                  {emailConfig.apiKey ? '✅' : '❌'}
                </div>
                <div className="text-xs text-muted-foreground">
                  {emailConfig.apiKey ? 'Configured' : 'Not Set'}
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="text-sm font-medium">From Email</div>
                <div className="text-sm font-mono bg-muted p-2 rounded">
                  {emailConfig.fromEmail}
                </div>
                <div className="text-xs text-muted-foreground">Sender Address</div>
              </div>
              
              <div className="space-y-2">
                <div className="text-sm font-medium">From Name</div>
                <div className="text-sm font-mono bg-muted p-2 rounded">
                  {emailConfig.fromName}
                </div>
                <div className="text-xs text-muted-foreground">Sender Name</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Status Messages */}
        {emailConfig.provider === 'demo' && (
          <Card className="border-yellow-200 bg-yellow-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-2 text-yellow-800">
                <TestTube className="h-5 w-5" />
                <div>
                  <div className="font-medium">Demo Mode Active</div>
                  <div className="text-sm">
                    Emails will be logged to the console instead of being sent. 
                    Configure a real email provider for production use.
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {emailConfig.provider !== 'demo' && !emailConfig.apiKey && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-2 text-red-800">
                <AlertCircle className="h-5 w-5" />
                <div>
                  <div className="font-medium">API Key Missing</div>
                  <div className="text-sm">
                    Set VITE_EMAIL_SERVICE_API_KEY in your .env.local file to enable email sending.
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {emailConfig.provider !== 'demo' && emailConfig.apiKey && (
          <Card className="border-green-200 bg-green-50">
            <CardContent className="pt-6">
              <div className="flex items-center gap-2 text-green-800">
                <CheckCircle className="h-5 w-5" />
                <div>
                  <div className="font-medium">Email Service Ready</div>
                  <div className="text-sm">
                    Email service is configured and ready to send notifications.
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <Separator />

        {/* Database Status Check */}
        <DatabaseTest />

        <Separator />

        {/* Comprehensive Email Test */}
        <ComprehensiveEmailTest />

        <Separator />

        {/* Individual Email Testing Component */}
        <EmailServiceTest />

        {/* Setup Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Setup Instructions</CardTitle>
            <CardDescription>
              How to configure email service for production
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-medium mb-2">1. Choose an Email Provider</h4>
              <p className="text-sm text-muted-foreground mb-2">
                EasTask supports multiple email providers:
              </p>
              <ul className="text-sm text-muted-foreground list-disc list-inside space-y-1">
                <li><strong>Resend</strong> - Recommended for modern applications</li>
                <li><strong>SendGrid</strong> - Enterprise-grade email delivery</li>
                <li><strong>Demo Mode</strong> - For development and testing</li>
              </ul>
            </div>

            <div>
              <h4 className="font-medium mb-2">2. Configure Environment Variables</h4>
              <p className="text-sm text-muted-foreground mb-2">
                Add these variables to your .env.local file:
              </p>
              <div className="bg-muted p-4 rounded-lg font-mono text-sm">
                <div>VITE_EMAIL_SERVICE_PROVIDER=resend</div>
                <div>VITE_EMAIL_SERVICE_API_KEY=your_api_key_here</div>
                <div>VITE_FROM_EMAIL=<EMAIL></div>
                <div>VITE_FROM_NAME=Your App Name</div>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">3. Test Email Functionality</h4>
              <p className="text-sm text-muted-foreground">
                Use the testing tools above to verify that emails are being sent correctly.
                Test both workspace invitations and task assignments.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </ModernLayout>
  );
};

export default EmailTest;
