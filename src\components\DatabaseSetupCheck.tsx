import React, { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuth } from '../contexts/SupabaseAuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  CheckCircle, 
  XCircle, 
  AlertTriangle, 
  Database, 
  Settings, 
  User,
  Activity,
  BarChart3,
  ExternalLink
} from 'lucide-react';

interface SetupStatus {
  userProfiles: boolean;
  userSettings: boolean;
  userActivity: boolean;
  userStatistics: boolean;
  storageAvatars: boolean;
  functions: boolean;
}

const DatabaseSetupCheck: React.FC = () => {
  const { user } = useAuth();
  const [setupStatus, setSetupStatus] = useState<SetupStatus>({
    userProfiles: false,
    userSettings: false,
    userActivity: false,
    userStatistics: false,
    storageAvatars: false,
    functions: false
  });
  const [loading, setLoading] = useState(true);
  const [showDetails, setShowDetails] = useState(false);

  useEffect(() => {
    checkDatabaseSetup();
  }, []);

  const checkDatabaseSetup = async () => {
    try {
      setLoading(true);
      const status: SetupStatus = {
        userProfiles: false,
        userSettings: false,
        userActivity: false,
        userStatistics: false,
        storageAvatars: false,
        functions: false
      };

      // Check if tables exist by trying to query them
      try {
        await supabase.from('user_profiles').select('id').limit(1);
        status.userProfiles = true;
      } catch (error) {
        console.log('user_profiles table not found');
      }

      try {
        await supabase.from('user_settings').select('id').limit(1);
        status.userSettings = true;
      } catch (error) {
        console.log('user_settings table not found');
      }

      try {
        await supabase.from('user_activity').select('id').limit(1);
        status.userActivity = true;
      } catch (error) {
        console.log('user_activity table not found');
      }

      try {
        await supabase.from('user_statistics').select('id').limit(1);
        status.userStatistics = true;
      } catch (error) {
        console.log('user_statistics table not found');
      }

      // Check storage bucket
      try {
        const { data, error } = await supabase.storage.listBuckets();
        if (data && data.some(bucket => bucket.name === 'avatars')) {
          status.storageAvatars = true;
        }
      } catch (error) {
        console.log('Storage check failed');
      }

      // Check functions (simplified check)
      status.functions = status.userProfiles; // If tables exist, functions likely exist too

      setSetupStatus(status);
    } catch (error) {
      console.error('Error checking database setup:', error);
    } finally {
      setLoading(false);
    }
  };

  const allTablesReady = setupStatus.userProfiles && setupStatus.userSettings && 
                        setupStatus.userActivity && setupStatus.userStatistics;

  const StatusIcon = ({ status }: { status: boolean }) => (
    status ? (
      <CheckCircle className="h-4 w-4 text-green-500" />
    ) : (
      <XCircle className="h-4 w-4 text-red-500" />
    )
  );

  if (loading) {
    return (
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="flex items-center gap-2">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-orange-500"></div>
            <span>Checking database setup...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (allTablesReady && setupStatus.storageAvatars) {
    return (
      <Card className="mb-6 border-green-200 bg-green-50">
        <CardContent className="p-4">
          <div className="flex items-center gap-2 text-green-700">
            <CheckCircle className="h-5 w-5" />
            <span className="font-medium">Database setup complete! All features are ready.</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mb-6 border-orange-200 bg-orange-50">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-orange-700">
          <AlertTriangle className="h-5 w-5" />
          Database Setup Required
        </CardTitle>
        <CardDescription>
          Some database tables are missing. Please run the setup script to enable all features.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Alert>
          <Database className="h-4 w-4" />
          <AlertDescription>
            <strong>Action Required:</strong> Run the SQL schema in your Supabase project to enable profile and settings functionality.
          </AlertDescription>
        </Alert>

        <div className="flex gap-2">
          <Button 
            variant="outline" 
            size="sm"
            onClick={() => setShowDetails(!showDetails)}
          >
            {showDetails ? 'Hide Details' : 'Show Details'}
          </Button>
          <Button 
            size="sm"
            onClick={() => window.open('https://app.supabase.com', '_blank')}
            className="bg-orange-500 hover:bg-orange-600"
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            Open Supabase
          </Button>
        </div>

        {showDetails && (
          <div className="space-y-3 pt-4 border-t">
            <h4 className="font-medium text-sm">Setup Status:</h4>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
              <div className="flex items-center justify-between p-2 rounded bg-white">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <span>User Profiles</span>
                </div>
                <StatusIcon status={setupStatus.userProfiles} />
              </div>
              
              <div className="flex items-center justify-between p-2 rounded bg-white">
                <div className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  <span>User Settings</span>
                </div>
                <StatusIcon status={setupStatus.userSettings} />
              </div>
              
              <div className="flex items-center justify-between p-2 rounded bg-white">
                <div className="flex items-center gap-2">
                  <Activity className="h-4 w-4" />
                  <span>User Activity</span>
                </div>
                <StatusIcon status={setupStatus.userActivity} />
              </div>
              
              <div className="flex items-center justify-between p-2 rounded bg-white">
                <div className="flex items-center gap-2">
                  <BarChart3 className="h-4 w-4" />
                  <span>User Statistics</span>
                </div>
                <StatusIcon status={setupStatus.userStatistics} />
              </div>
              
              <div className="flex items-center justify-between p-2 rounded bg-white">
                <div className="flex items-center gap-2">
                  <Database className="h-4 w-4" />
                  <span>Storage (Avatars)</span>
                </div>
                <StatusIcon status={setupStatus.storageAvatars} />
              </div>
              
              <div className="flex items-center justify-between p-2 rounded bg-white">
                <div className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  <span>Functions</span>
                </div>
                <StatusIcon status={setupStatus.functions} />
              </div>
            </div>

            <div className="text-xs text-gray-600 space-y-1">
              <p><strong>Next Steps:</strong></p>
              <p>1. Open the file <code>PROFILE_SETTINGS_SCHEMA.sql</code></p>
              <p>2. Copy all content and run it in Supabase SQL Editor</p>
              <p>3. Create an 'avatars' storage bucket in Supabase Storage</p>
              <p>4. Refresh this page to verify setup</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DatabaseSetupCheck;
