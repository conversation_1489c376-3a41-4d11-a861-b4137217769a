import React, { useState } from 'react';
import { But<PERSON> } from './ui/button';
import { Input } from './ui/input';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './ui/card';
import { emailService } from '../services/emailService';
import { toast } from 'sonner';
import { Mail, Send, CheckCircle, AlertCircle } from 'lucide-react';

const EmailTest: React.FC = () => {
  const [testEmail, setTestEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [lastResult, setLastResult] = useState<boolean | null>(null);

  const handleTestInvitation = async () => {
    if (!testEmail) {
      toast.error('Please enter an email address');
      return;
    }

    setLoading(true);
    try {
      const success = await emailService.sendWorkspaceInvitation({
        workspaceName: 'Test Workspace',
        inviterName: 'Test User',
        inviterEmail: '<EMAIL>',
        invitedEmail: testEmail,
        inviteCode: 'TEST123',
        role: 'member',
        workspaceDescription: 'This is a test workspace invitation'
      });

      setLastResult(success);
      if (success) {
        toast.success('Test invitation email sent successfully!');
      } else {
        toast.error('Failed to send test email');
      }
    } catch (error) {
      console.error('Email test error:', error);
      toast.error('Error sending test email');
      setLastResult(false);
    } finally {
      setLoading(false);
    }
  };

  const handleTestTaskAssignment = async () => {
    if (!testEmail) {
      toast.error('Please enter an email address');
      return;
    }

    setLoading(true);
    try {
      const success = await emailService.sendTaskAssignment({
        taskId: 'test-task-123',
        taskTitle: 'Test Task Assignment',
        taskDescription: 'This is a test task assignment email',
        workspaceName: 'Test Workspace',
        assignerName: 'Test Manager',
        assigneeEmail: testEmail,
        dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        priority: 'High',
        pageTitle: 'Test Page'
      });

      setLastResult(success);
      if (success) {
        toast.success('Test task assignment email sent successfully!');
      } else {
        toast.error('Failed to send test email');
      }
    } catch (error) {
      console.error('Email test error:', error);
      toast.error('Error sending test email');
      setLastResult(false);
    } finally {
      setLoading(false);
    }
  };

  const emailConfig = {
    provider: import.meta.env.VITE_EMAIL_SERVICE_PROVIDER || 'demo',
    hasApiKey: !!import.meta.env.VITE_EMAIL_SERVICE_API_KEY,
    fromEmail: import.meta.env.VITE_FROM_EMAIL || '<EMAIL>',
    fromName: import.meta.env.VITE_FROM_NAME || 'EasTask Team'
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Mail className="w-5 h-5" />
          Email Service Test
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Configuration Status */}
        <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded-lg space-y-2">
          <h3 className="font-semibold text-sm">Current Configuration:</h3>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="text-gray-600 dark:text-gray-400">Provider:</span>
              <span className="ml-2 font-mono">{emailConfig.provider}</span>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">API Key:</span>
              <span className="ml-2">
                {emailConfig.hasApiKey ? (
                  <span className="text-green-600 flex items-center gap-1">
                    <CheckCircle className="w-3 h-3" />
                    Configured
                  </span>
                ) : (
                  <span className="text-orange-600 flex items-center gap-1">
                    <AlertCircle className="w-3 h-3" />
                    Demo Mode
                  </span>
                )}
              </span>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">From Email:</span>
              <span className="ml-2 font-mono text-xs">{emailConfig.fromEmail}</span>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">From Name:</span>
              <span className="ml-2">{emailConfig.fromName}</span>
            </div>
          </div>
        </div>

        {/* Test Email Input */}
        <div className="space-y-2">
          <label className="text-sm font-medium">Test Email Address:</label>
          <Input
            type="email"
            placeholder="Enter email to test..."
            value={testEmail}
            onChange={(e) => setTestEmail(e.target.value)}
            className="w-full"
          />
        </div>

        {/* Test Buttons */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Button
            onClick={handleTestInvitation}
            disabled={loading || !testEmail}
            className="w-full"
            variant="outline"
          >
            <Send className="w-4 h-4 mr-2" />
            Test Invitation Email
          </Button>
          <Button
            onClick={handleTestTaskAssignment}
            disabled={loading || !testEmail}
            className="w-full"
            variant="outline"
          >
            <Send className="w-4 h-4 mr-2" />
            Test Task Assignment
          </Button>
        </div>

        {/* Last Result */}
        {lastResult !== null && (
          <div className={`p-3 rounded-lg ${
            lastResult 
              ? 'bg-green-50 dark:bg-green-950 text-green-800 dark:text-green-200' 
              : 'bg-red-50 dark:bg-red-950 text-red-800 dark:text-red-200'
          }`}>
            <div className="flex items-center gap-2">
              {lastResult ? (
                <CheckCircle className="w-4 h-4" />
              ) : (
                <AlertCircle className="w-4 h-4" />
              )}
              <span className="text-sm font-medium">
                {lastResult ? 'Email sent successfully!' : 'Email sending failed'}
              </span>
            </div>
          </div>
        )}

        {/* Instructions */}
        <div className="bg-blue-50 dark:bg-blue-950 p-4 rounded-lg">
          <h4 className="font-semibold text-sm text-blue-800 dark:text-blue-200 mb-2">
            Setup Instructions:
          </h4>
          <ol className="text-sm text-blue-700 dark:text-blue-300 space-y-1 list-decimal list-inside">
            <li>Sign up at <a href="https://resend.com" target="_blank" rel="noopener noreferrer" className="underline">resend.com</a></li>
            <li>Get your API key from the dashboard</li>
            <li>Update VITE_EMAIL_SERVICE_API_KEY in .env.local</li>
            <li>Change VITE_EMAIL_SERVICE_PROVIDER to 'resend'</li>
            <li>Test with your email address</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  );
};

export default EmailTest;
