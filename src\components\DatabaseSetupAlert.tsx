import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { AlertTriangle, Database, ExternalLink, Copy, CheckCircle } from 'lucide-react';
import { toast } from 'sonner';

const DatabaseSetupAlert: React.FC = () => {
  const [copied, setCopied] = useState(false);

  const setupScript = `-- =====================================================
-- SETUP DATABASE FOR EASTASK
-- =====================================================
-- Run this in Supabase SQL Editor to set up all required tables

-- =====================================================
-- 1. CREATE NOTIFICATIONS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN (
    'invitation', 
    'task_assignment', 
    'task_update', 
    'workspace_update', 
    'mention', 
    'system',
    'task_comment',
    'task_status_change',
    'workspace_invitation'
  )),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  data JSONB DEFAULT '{}'::jsonb,
  read BOOLEAN DEFAULT FALSE,
  action_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 2. CREATE INDEXES
-- =====================================================
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_workspace_id ON notifications(workspace_id);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read);
CREATE INDEX IF NOT EXISTS idx_notifications_user_read_created ON notifications(user_id, read, created_at DESC);

-- =====================================================
-- 3. ENABLE RLS
-- =====================================================
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 4. CREATE RLS POLICIES
-- =====================================================
-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own notifications" ON notifications;
DROP POLICY IF EXISTS "Users can create notifications" ON notifications;
DROP POLICY IF EXISTS "Users can update their own notifications" ON notifications;
DROP POLICY IF EXISTS "Users can delete their own notifications" ON notifications;

-- Create new policies
CREATE POLICY "Users can view their own notifications" ON notifications
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can create notifications" ON notifications
  FOR INSERT WITH CHECK (true); -- Allow system to create notifications for any user

CREATE POLICY "Users can update their own notifications" ON notifications
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own notifications" ON notifications
  FOR DELETE USING (user_id = auth.uid());

-- =====================================================
-- 5. CREATE NOTIFICATION FUNCTIONS
-- =====================================================

-- Function to get unread notification count
CREATE OR REPLACE FUNCTION get_unread_notification_count(p_user_id UUID DEFAULT NULL)
RETURNS INTEGER AS $$
DECLARE
  target_user_id UUID;
  unread_count INTEGER;
BEGIN
  target_user_id := COALESCE(p_user_id, auth.uid());
  
  SELECT COUNT(*) INTO unread_count
  FROM notifications 
  WHERE user_id = target_user_id AND read = FALSE;
  
  RETURN unread_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark notification as read
CREATE OR REPLACE FUNCTION mark_notification_read(p_notification_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE notifications 
  SET read = TRUE 
  WHERE id = p_notification_id AND user_id = auth.uid();
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark all notifications as read
CREATE OR REPLACE FUNCTION mark_all_notifications_read(p_user_id UUID DEFAULT NULL)
RETURNS INTEGER AS $$
DECLARE
  target_user_id UUID;
  updated_count INTEGER;
BEGIN
  target_user_id := COALESCE(p_user_id, auth.uid());
  
  UPDATE notifications 
  SET read = TRUE 
  WHERE user_id = target_user_id AND read = FALSE;
  
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  RETURN updated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 6. UPDATE USER_ACTIVITY TABLE (ADD DETAILS COLUMN IF MISSING)
-- =====================================================
-- Add details column to user_activity table if it doesn't exist
DO $$
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM information_schema.columns
    WHERE table_name = 'user_activity'
    AND column_name = 'details'
    AND table_schema = 'public'
  ) THEN
    ALTER TABLE user_activity ADD COLUMN details JSONB DEFAULT '{}'::jsonb;
    CREATE INDEX IF NOT EXISTS idx_user_activity_details ON user_activity USING gin(details);
  END IF;
END $$;

-- =====================================================
-- 7. ENABLE REALTIME
-- =====================================================
ALTER PUBLICATION supabase_realtime ADD TABLE notifications;

-- =====================================================
-- 8. CREATE SAMPLE NOTIFICATION FOR TESTING
-- =====================================================
-- Only create if user is authenticated
DO $$
BEGIN
  IF auth.uid() IS NOT NULL THEN
    INSERT INTO notifications (
      user_id, 
      type, 
      title, 
      message, 
      data
    ) VALUES (
      auth.uid(),
      'system',
      'Welcome to EasTask!',
      'Your notification system is now set up and working perfectly.',
      '{"setup": true, "version": "1.0"}'::jsonb
    );
  END IF;
EXCEPTION WHEN OTHERS THEN
  -- Ignore errors if user not authenticated
  NULL;
END $$;

-- Success message
SELECT '🎉 Database setup complete! Notifications system is ready.' as result;`;

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(setupScript);
      setCopied(true);
      toast.success('SQL script copied to clipboard!');
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      toast.error('Failed to copy to clipboard');
    }
  };

  const openSupabase = () => {
    window.open('https://supabase.com/dashboard', '_blank');
  };

  return (
    <Alert className="border-orange-200 bg-orange-50">
      <AlertTriangle className="h-4 w-4 text-orange-600" />
      <AlertDescription>
        <div className="space-y-4">
          <div>
            <div className="font-medium text-orange-800">Database Setup Required</div>
            <div className="text-sm text-orange-700 mt-1">
              Some database tables are missing. Please run the setup script to enable all features.
            </div>
          </div>

          <Card className="border-orange-200">
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Database className="h-4 w-4" />
                Setup Instructions
              </CardTitle>
              <CardDescription className="text-xs">
                Follow these steps to set up the database schema
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">1</Badge>
                  <span>Copy the SQL setup script below</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">2</Badge>
                  <span>Open your Supabase dashboard</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">3</Badge>
                  <span>Go to SQL Editor and paste the script</span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="w-6 h-6 p-0 flex items-center justify-center text-xs">4</Badge>
                  <span>Run the script to create tables and functions</span>
                </div>
              </div>

              <Separator />

              <div className="flex gap-2">
                <Button 
                  onClick={copyToClipboard}
                  size="sm"
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  {copied ? (
                    <CheckCircle className="h-3 w-3 text-green-500" />
                  ) : (
                    <Copy className="h-3 w-3" />
                  )}
                  {copied ? 'Copied!' : 'Copy SQL Script'}
                </Button>
                
                <Button 
                  onClick={openSupabase}
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <ExternalLink className="h-3 w-3" />
                  Open Supabase
                </Button>
              </div>

              <div className="text-xs text-muted-foreground">
                <strong>Note:</strong> After running the script, refresh this page to see the changes.
              </div>
            </CardContent>
          </Card>
        </div>
      </AlertDescription>
    </Alert>
  );
};

export default DatabaseSetupAlert;
