
@import url('https://fonts.googleapis.com/css2?family=DM+Sans:ital,opsz,wght@0,9..40,100..1000;1,9..40,100..1000&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer utilities {
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
}

@layer base {
  :root {
    /* Light Mode - Orange Theme */
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 24 95% 53%; /* Orange primary */
    --primary-foreground: 0 0% 100%;
    --secondary: 24 100% 97%; /* Light orange */
    --secondary-foreground: 24 95% 53%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 24 100% 95%; /* Very light orange */
    --accent-foreground: 24 95% 53%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 24 95% 53%;
    --radius: 0.75rem;

    /* Custom Orange Shades */
    --orange-50: 24 100% 97%;
    --orange-100: 24 100% 94%;
    --orange-200: 24 100% 87%;
    --orange-300: 24 95% 76%;
    --orange-400: 24 95% 64%;
    --orange-500: 24 95% 53%; /* Main orange */
    --orange-600: 24 95% 45%;
    --orange-700: 24 95% 37%;
    --orange-800: 24 95% 29%;
    --orange-900: 24 95% 21%;

    /* Task status colors */
    --task-todo: 217 91% 60%;
    --task-progress: 45 93% 58%;
    --task-done: 142 71% 45%;

    /* Brand colors */
    --coral-orange: 24 95% 53%;
    --cornflower-blue: 217 91% 60%;

    /* Chart colors */
    --chart-1: 24 95% 53%;
    --chart-2: 217 91% 60%;
    --chart-3: 142 71% 45%;
    --chart-4: 45 93% 58%;
    --chart-5: 280 65% 60%;
  }

  .dark {
    /* Dark Mode - Orange Theme */
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 6%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 24 95% 53%; /* Keep orange primary */
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 24 95% 53%;

    /* Custom Orange Shades for Dark Mode */
    --orange-50: 24 95% 21%;
    --orange-100: 24 95% 29%;
    --orange-200: 24 95% 37%;
    --orange-300: 24 95% 45%;
    --orange-400: 24 95% 53%;
    --orange-500: 24 95% 53%; /* Main orange */
    --orange-600: 24 95% 64%;
    --orange-700: 24 95% 76%;
    --orange-800: 24 100% 87%;
    --orange-900: 24 100% 94%;

    /* Task status colors for dark mode */
    --task-todo: 217 91% 60%;
    --task-progress: 45 93% 58%;
    --task-done: 142 71% 45%;

    /* Brand colors for dark mode */
    --coral-orange: 24 95% 53%;
    --cornflower-blue: 217 91% 60%;

    /* Chart colors for dark mode */
    --chart-1: 24 95% 53%;
    --chart-2: 217 91% 60%;
    --chart-3: 142 71% 45%;
    --chart-4: 45 93% 58%;
    --chart-5: 280 65% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-family: 'DM Sans', sans-serif;
  }
}

@layer components {
  .drag-over {
    @apply ring-2 ring-primary ring-opacity-50 bg-accent;
  }

  .dragging {
    @apply opacity-50 transform rotate-1;
  }

  /* Orange gradient backgrounds */
  .gradient-orange {
    background: linear-gradient(135deg, hsl(var(--orange-500)) 0%, hsl(var(--orange-600)) 100%);
  }

  .gradient-orange-light {
    background: linear-gradient(135deg, hsl(var(--orange-50)) 0%, hsl(var(--orange-100)) 100%);
  }

  .gradient-orange-subtle {
    background: linear-gradient(135deg, hsl(var(--orange-100)) 0%, hsl(var(--orange-200)) 100%);
  }

  /* Modern card styles */
  .card-modern {
    @apply bg-card border border-border rounded-xl shadow-sm hover:shadow-md transition-all duration-200;
  }

  .card-hover {
    @apply hover:shadow-lg hover:-translate-y-1 transition-all duration-300;
  }

  /* Button variants */
  .btn-orange {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 shadow-sm hover:shadow-md transition-all duration-200;
  }

  .btn-orange-outline {
    @apply border-2 border-primary text-primary hover:bg-primary hover:text-primary-foreground transition-all duration-200;
  }

  /* Status indicators */
  .status-todo {
    @apply bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200;
  }

  .status-progress {
    @apply bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200;
  }

  .status-done {
    @apply bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200;
  }

  /* Modern sidebar */
  .sidebar-modern {
    @apply bg-card border-r border-border backdrop-blur-sm;
  }

  /* Glass effect */
  .glass {
    @apply bg-white/80 dark:bg-gray-900/80 backdrop-blur-md border border-white/20 dark:border-gray-700/20;
  }

  /* Drag and Drop Styles */
  .dragging {
    @apply opacity-50 rotate-1 transition-all duration-200 z-50;
  }

  .drag-over {
    @apply bg-orange-100 border-2 border-dashed border-orange-500 rounded-lg transition-all duration-200;
  }

  .dark .drag-over {
    @apply bg-orange-900/20 border-orange-400;
  }

  .drop-zone {
    @apply min-h-[100px] border-2 border-dashed border-border rounded-lg flex items-center justify-center transition-all duration-200;
  }

  .drop-zone:hover {
    @apply border-orange-500 bg-orange-50;
  }

  .dark .drop-zone:hover {
    @apply border-orange-400 bg-orange-900/10;
  }

  .drop-zone.active {
    @apply border-orange-500 bg-orange-100 scale-105;
  }

  .dark .drop-zone.active {
    @apply border-orange-400 bg-orange-900/20;
  }
}
