# 📧 **EasTask Email System Setup Guide**

## 🎯 **Overview**

This guide will help you set up the complete email notification system for EasTask, including:

- ✅ **Resend Email Service Integration**
- ✅ **Workspace Invitation Emails**
- ✅ **Task Assignment Notifications**
- ✅ **Task Status Change Notifications**
- ✅ **Comment Notifications**
- ✅ **Real-time Notification Center**

---

## 🚀 **Step 1: Database Setup**

### **1.1 Run Notifications Schema**
```sql
-- Copy and run NOTIFICATIONS_SCHEMA.sql in Supabase SQL Editor
-- This creates the notifications table and all related functions
```

### **1.2 Verify Database Setup**
After running the schema, verify it worked:
```sql
-- Check if notifications table exists
SELECT * FROM notifications LIMIT 1;

-- Check if functions exist
SELECT routine_name FROM information_schema.routines 
WHERE routine_name LIKE '%notification%';
```

---

## 📧 **Step 2: Configure Resend Email Service**

### **2.1 Get Resend API Key**
1. Go to [resend.com](https://resend.com)
2. Sign up for a free account
3. Go to **API Keys** section
4. Create a new API key
5. Copy the API key (starts with `re_`)

### **2.2 Update Environment Variables**
Your `.env.local` file should have:
```env
# Email Service Configuration
VITE_EMAIL_SERVICE_PROVIDER=resend
VITE_EMAIL_SERVICE_API_KEY=re_your_api_key_here
VITE_FROM_EMAIL=<EMAIL>
VITE_FROM_NAME=EasTask Team
VITE_APP_URL=http://localhost:8081
```

### **2.3 Custom Domain (Optional)**
For production, set up a custom domain:
1. Add your domain in Resend dashboard
2. Configure DNS records
3. Update `VITE_FROM_EMAIL` to use your domain

---

## 🧪 **Step 3: Test Email System**

### **3.1 Access Email Testing Page**
1. Start your development server: `npm run dev`
2. Navigate to: `http://localhost:8081/email-test`
3. You'll see the email testing interface

### **3.2 Test Connection**
1. Click **"Test Connection"** button
2. Should show: ✅ **"Resend API key is valid and service is ready"**
3. If it fails, check your API key

### **3.3 Test Workspace Invitation**
1. Enter a test email address
2. Select a role (Member/Admin)
3. Click **"Send Test Invitation"**
4. Check the email inbox for the invitation

### **3.4 Test Task Assignment**
1. Enter a test email address
2. Modify task details if needed
3. Click **"Send Test Assignment"**
4. Check the email inbox for the task notification

---

## 🔄 **Step 4: Enable Real-time Notifications**

### **4.1 Notification Center**
The notification center is already integrated and will:
- Show real-time notifications
- Display unread count badge
- Allow marking notifications as read
- Provide action links to relevant pages

### **4.2 Real-time Updates**
Notifications appear instantly when:
- Someone invites you to a workspace
- A task is assigned to you
- Task status changes on tasks you're involved with
- Someone comments on your tasks

---

## 📋 **Step 5: Verify Complete Workflow**

### **5.1 Workspace Invitation Flow**
1. **Create Workspace**: Go to Home → Create New Workspace
2. **Invite Member**: Workspace Management → Invite New Member
3. **Check Email**: Invitation email should be sent
4. **Join Workspace**: Click link in email → Should join successfully

### **5.2 Task Assignment Flow**
1. **Create Task**: Go to Tasks → Add New Task
2. **Assign Task**: Edit task → Assign to team member
3. **Check Email**: Assignment email should be sent
4. **Check Notifications**: Assignee should see notification

### **5.3 Task Status Flow**
1. **Update Task**: Change task status (To Do → In Progress → Done)
2. **Check Notifications**: Relevant users should see status change notifications
3. **Check Activity**: Activity should be logged in workspace

---

## 🎨 **Step 6: Email Templates**

### **6.1 Available Templates**
- ✅ **Workspace Invitation**: Professional invitation with workspace details
- ✅ **Task Assignment**: Task details with due date and priority
- ✅ **Task Reminder**: Due date reminders
- ✅ **Password Reset**: Secure password reset emails

### **6.2 Template Features**
- 🎨 **Branded Design**: Orange color scheme matching EasTask
- 📱 **Mobile Responsive**: Works on all devices
- 🔗 **Action Buttons**: Direct links to relevant pages
- 🛡️ **Security**: Secure links with expiration

---

## 🔧 **Step 7: Production Deployment**

### **7.1 Environment Variables for Production**
```env
# Production Email Configuration
VITE_EMAIL_SERVICE_PROVIDER=resend
VITE_EMAIL_SERVICE_API_KEY=re_your_production_api_key
VITE_FROM_EMAIL=<EMAIL>
VITE_FROM_NAME=Your App Name
VITE_APP_URL=https://your-app.netlify.app
```

### **7.2 Domain Verification**
1. Add your domain to Resend
2. Configure SPF, DKIM, and DMARC records
3. Verify domain in Resend dashboard
4. Update `VITE_FROM_EMAIL` to use verified domain

### **7.3 Rate Limits**
- **Free Plan**: 100 emails/day, 3,000 emails/month
- **Pro Plan**: Higher limits for production use
- Monitor usage in Resend dashboard

---

## 📊 **Step 8: Monitoring & Analytics**

### **8.1 Email Delivery Monitoring**
- Check Resend dashboard for delivery status
- Monitor bounce and complaint rates
- Set up webhooks for delivery events

### **8.2 Notification Analytics**
- Track notification open rates
- Monitor user engagement with notifications
- Analyze most effective notification types

---

## 🆘 **Troubleshooting**

### **Common Issues:**

#### **"API Key Invalid" Error**
- Verify API key is correct and starts with `re_`
- Check if API key has proper permissions
- Ensure no extra spaces in environment variable

#### **"Email Not Received" Issue**
- Check spam/junk folder
- Verify email address is correct
- Check Resend dashboard for delivery status
- Ensure domain is verified (for custom domains)

#### **"Notification Not Showing" Issue**
- Check browser console for errors
- Verify notifications table exists in database
- Check if real-time subscriptions are enabled
- Ensure user is authenticated

#### **"Demo Mode Active" Warning**
- Set `VITE_EMAIL_SERVICE_API_KEY` in environment
- Restart development server
- Verify provider is set to 'resend'

---

## ✅ **Success Checklist**

**Email System is working when:**
- [ ] Connection test shows ✅ "Service Ready"
- [ ] Test invitation emails are received
- [ ] Test task assignment emails are received
- [ ] Notification center shows real-time updates
- [ ] Workspace invitation flow works end-to-end
- [ ] Task assignment notifications work
- [ ] Status change notifications work
- [ ] No errors in browser console

---

## 🎉 **Congratulations!**

Your EasTask email system is now fully configured and ready for production use! 

**Features Now Available:**
- 📧 **Professional Email Templates**
- 🔔 **Real-time Notifications**
- 👥 **Workspace Collaboration**
- 📋 **Task Management Notifications**
- 🎨 **Branded Email Design**
- 📱 **Mobile-Responsive Emails**

**Ready for team collaboration!** 🚀
