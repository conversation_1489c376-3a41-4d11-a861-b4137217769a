import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { emailService } from '../services/emailService';
import { notificationService } from '../services/notificationService';
import { useSupabaseAuth } from '../contexts/SupabaseAuthContext';
import { useSupabaseWorkspace } from '../contexts/SupabaseWorkspaceContext';
import { supabase } from '../lib/supabase';
import { 
  Mail, 
  Send, 
  CheckCircle, 
  XCircle, 
  Loader2, 
  TestTube,
  Users,
  MessageSquare,
  Bell,
  AlertTriangle
} from 'lucide-react';
import { toast } from 'sonner';

interface TestResult {
  name: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  details?: string;
}

const ComprehensiveEmailTest: React.FC = () => {
  const { user } = useSupabaseAuth();
  const { currentWorkspace } = useSupabaseWorkspace();
  
  const [testEmail, setTestEmail] = useState('');
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);

  const updateTestResult = (name: string, status: 'success' | 'error', message: string, details?: string) => {
    setTestResults(prev => {
      const existing = prev.find(r => r.name === name);
      if (existing) {
        existing.status = status;
        existing.message = message;
        existing.details = details;
        return [...prev];
      } else {
        return [...prev, { name, status, message, details }];
      }
    });
  };

  const runComprehensiveTest = async () => {
    if (!testEmail) {
      toast.error('Please enter a test email address');
      return;
    }

    if (!user || !currentWorkspace) {
      toast.error('Please sign in and select a workspace');
      return;
    }

    setIsRunningTests(true);
    setTestResults([]);

    try {
      // Test 1: Email Service Connection
      updateTestResult('connection', 'pending', 'Testing email service connection...');
      try {
        const connectionResult = await emailService.testConnection();
        if (connectionResult.success) {
          updateTestResult('connection', 'success', `Connected to ${connectionResult.provider}`, connectionResult.message);
        } else {
          updateTestResult('connection', 'error', 'Connection failed', connectionResult.message);
        }
      } catch (error) {
        updateTestResult('connection', 'error', 'Connection test failed', String(error));
      }

      // Test 2: Database Tables Check
      updateTestResult('database', 'pending', 'Checking database tables...');
      try {
        const { error: notifError } = await supabase.from('notifications').select('id').limit(1);
        const { error: workspaceError } = await supabase.from('workspaces').select('id').limit(1);
        const { error: tasksError } = await supabase.from('tasks').select('id').limit(1);

        if (!notifError && !workspaceError && !tasksError) {
          updateTestResult('database', 'success', 'All required tables exist');
        } else {
          updateTestResult('database', 'error', 'Some tables are missing', 
            `Notifications: ${!notifError ? '✅' : '❌'}, Workspaces: ${!workspaceError ? '✅' : '❌'}, Tasks: ${!tasksError ? '✅' : '❌'}`);
        }
      } catch (error) {
        updateTestResult('database', 'error', 'Database check failed', String(error));
      }

      // Test 3: Workspace Invitation Email
      updateTestResult('invitation', 'pending', 'Testing workspace invitation email...');
      try {
        const inviteSuccess = await notificationService.sendWorkspaceInvitation({
          invitedEmail: testEmail,
          inviterUserId: user.id,
          workspaceId: currentWorkspace.id,
          workspaceName: currentWorkspace.name,
          inviterName: user.user_metadata?.full_name || user.email || 'Test User',
          inviterEmail: user.email,
          role: 'member',
          inviteCode: currentWorkspace.invite_code || 'test-code'
        });

        if (inviteSuccess) {
          updateTestResult('invitation', 'success', `Invitation email sent to ${testEmail}`);
        } else {
          updateTestResult('invitation', 'error', 'Failed to send invitation email');
        }
      } catch (error) {
        updateTestResult('invitation', 'error', 'Invitation test failed', String(error));
      }

      // Test 4: Task Assignment Email
      updateTestResult('task_assignment', 'pending', 'Testing task assignment email...');
      try {
        await notificationService.sendTaskAssignment({
          taskId: 'test-task-' + Date.now(),
          taskTitle: 'Test Task Assignment',
          taskDescription: 'This is a test task assignment to verify email functionality.',
          assigneeUserId: 'test-user-id',
          assigneeEmail: testEmail,
          assignerUserId: user.id,
          assignerName: user.user_metadata?.full_name || user.email || 'Test User',
          workspaceId: currentWorkspace.id,
          workspaceName: currentWorkspace.name,
          pageTitle: 'Test Page',
          dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          priority: 'medium'
        });

        updateTestResult('task_assignment', 'success', `Task assignment email sent to ${testEmail}`);
      } catch (error) {
        updateTestResult('task_assignment', 'error', 'Task assignment test failed', String(error));
      }

      // Test 5: Task Status Change Notification
      updateTestResult('status_change', 'pending', 'Testing task status change notification...');
      try {
        await notificationService.sendTaskStatusChange({
          taskId: 'test-task-status-' + Date.now(),
          taskTitle: 'Test Task Status Change',
          oldStatus: 'pending',
          newStatus: 'in_progress',
          updaterUserId: user.id,
          updaterName: user.user_metadata?.full_name || user.email || 'Test User',
          workspaceId: currentWorkspace.id,
          notifyUserIds: [user.id] // Self-notification for testing
        });

        updateTestResult('status_change', 'success', 'Task status change notification created');
      } catch (error) {
        updateTestResult('status_change', 'error', 'Status change test failed', String(error));
      }

      // Test 6: Task Comment Notification
      updateTestResult('comment', 'pending', 'Testing task comment notification...');
      try {
        await notificationService.sendTaskComment({
          taskId: 'test-task-comment-' + Date.now(),
          taskTitle: 'Test Task Comment',
          commentId: 'test-comment-' + Date.now(),
          commentText: 'This is a test comment to verify notification functionality.',
          commenterUserId: user.id,
          commenterName: user.user_metadata?.full_name || user.email || 'Test User',
          workspaceId: currentWorkspace.id,
          workspaceName: currentWorkspace.name,
          notifyUserIds: [user.id] // Self-notification for testing
        });

        updateTestResult('comment', 'success', 'Task comment notification created');
      } catch (error) {
        updateTestResult('comment', 'error', 'Comment test failed', String(error));
      }

      // Test 7: Notification Center Check
      updateTestResult('notification_center', 'pending', 'Testing notification center...');
      try {
        const notifications = await notificationService.getUserNotifications(user.id, 5);
        updateTestResult('notification_center', 'success', `Retrieved ${notifications.length} notifications`);
      } catch (error) {
        updateTestResult('notification_center', 'error', 'Notification center test failed', String(error));
      }

      toast.success('Comprehensive email test completed!');
    } catch (error) {
      console.error('Test suite error:', error);
      toast.error('Test suite failed');
    } finally {
      setIsRunningTests(false);
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'pending':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
    }
  };

  const getStatusBadge = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <Badge variant="default" className="bg-green-500">Success</Badge>;
      case 'error':
        return <Badge variant="destructive">Failed</Badge>;
      case 'pending':
        return <Badge variant="secondary">Testing...</Badge>;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TestTube className="h-5 w-5" />
          Comprehensive Email System Test
        </CardTitle>
        <CardDescription>
          Run a complete test of all email functionality in EasTask
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="test-email">Test Email Address</Label>
          <Input
            id="test-email"
            type="email"
            placeholder="<EMAIL>"
            value={testEmail}
            onChange={(e) => setTestEmail(e.target.value)}
          />
          <p className="text-sm text-muted-foreground">
            Enter your email address to receive test emails
          </p>
        </div>

        <Button 
          onClick={runComprehensiveTest}
          disabled={isRunningTests || !testEmail || !user || !currentWorkspace}
          className="w-full"
        >
          {isRunningTests ? (
            <Loader2 className="h-4 w-4 animate-spin mr-2" />
          ) : (
            <TestTube className="h-4 w-4 mr-2" />
          )}
          Run Comprehensive Test
        </Button>

        {!user && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Please sign in to run email tests
            </AlertDescription>
          </Alert>
        )}

        {!currentWorkspace && user && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Please select a workspace to run email tests
            </AlertDescription>
          </Alert>
        )}

        {testResults.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium">Test Results</h4>
            {testResults.map((result, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  {getStatusIcon(result.status)}
                  <div>
                    <div className="font-medium capitalize">{result.name.replace('_', ' ')}</div>
                    <div className="text-sm text-muted-foreground">{result.message}</div>
                    {result.details && (
                      <div className="text-xs text-muted-foreground mt-1">{result.details}</div>
                    )}
                  </div>
                </div>
                {getStatusBadge(result.status)}
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default ComprehensiveEmailTest;
