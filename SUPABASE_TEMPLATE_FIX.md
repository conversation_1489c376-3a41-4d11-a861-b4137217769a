# 🔧 URGENT FIX: Supabase Email Template Configuration

## 🚨 **ISSUE IDENTIFIED**
Your password reset is failing because the Supabase email template is missing the `refresh_token` parameter.

## ✅ **IMMEDIATE SOLUTION**

### **Step 1: Access Supabase Dashboard**
1. Go to: https://app.supabase.com
2. Select your EasTask project
3. Navigate to: **Authentication** → **Email Templates**

### **Step 2: Fix Password Recovery Template**
1. Click on **"Password Recovery"** template
2. **REPLACE** the entire template with this corrected version:

```html
<h2>Reset Your EasTask Password</h2>

<p>Hi there,</p>

<p>We received a request to reset your password for your EasTask account. Click the button below to create a new password:</p>

<p><a href="{{ .SiteURL }}/reset-password?access_token={{ .TokenHash }}&refresh_token={{ .RefreshTokenHash }}&type=recovery">Reset Password</a></p>

<p>This link will expire in {{ .ExpiresIn }} for security reasons.</p>

<p>If you didn't request a password reset, you can safely ignore this email.</p>

<p>Best regards,<br>
The EasTask Team</p>
```

### **Step 3: Verify URL Parameters**
**CRITICAL:** Make sure the URL includes ALL THREE parameters:
- `access_token={{ .TokenHash }}`
- `refresh_token={{ .RefreshTokenHash }}` ← **THIS WAS MISSING**
- `type=recovery`

### **Step 4: Save and Test**
1. **Save** the template in Supabase
2. **Request a NEW password reset** from your app
3. **Check the email** - the URL should now include all parameters

## 🔍 **VERIFICATION CHECKLIST**

After fixing the template, the reset URL should look like:
```
http://localhost:8081/reset-password?access_token=TOKEN&refresh_token=TOKEN&type=recovery
```

### **Before Fix (BROKEN):**
```
❌ Missing refresh_token parameter
http://localhost:8081/reset-password?access_token=TOKEN&type=recovery
```

### **After Fix (WORKING):**
```
✅ All parameters present
http://localhost:8081/reset-password?access_token=TOKEN&refresh_token=TOKEN&type=recovery
```

## 🚀 **TESTING STEPS**

1. **Save the corrected template** in Supabase
2. **Go to your app**: http://localhost:8081
3. **Click "Forgot Password?"**
4. **Enter your email** and submit
5. **Check your email** for the reset link
6. **Click the reset link** - it should now work!

## 🔧 **ADDITIONAL SETTINGS TO VERIFY**

### **Site URL Configuration:**
- Go to: **Authentication** → **Settings** → **General**
- **Site URL**: `http://localhost:8081`

### **Redirect URLs:**
- Go to: **Authentication** → **Settings** → **General**
- **Additional Redirect URLs**: `http://localhost:8081/reset-password`

## 🆘 **IF STILL NOT WORKING**

1. **Clear browser cache** and try again
2. **Use a different email** (old reset emails won't work)
3. **Check Supabase logs**: Dashboard → Logs
4. **Verify template was saved** correctly

## 📧 **ENHANCED TEMPLATE (OPTIONAL)**

For a more professional look, you can use this enhanced template:

```html
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Reset Your EasTask Password</title>
</head>
<body style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
    <div style="background: linear-gradient(135deg, #f97316 0%, #ea580c 100%); padding: 40px 20px; text-align: center; border-radius: 10px 10px 0 0;">
        <h1 style="color: white; margin: 0; font-size: 28px;">EasTask</h1>
        <p style="color: white; margin: 10px 0 0 0; opacity: 0.9;">Professional Task Management</p>
    </div>
    
    <div style="background: #ffffff; padding: 40px 20px; border: 1px solid #e5e7eb; border-top: none;">
        <h2 style="color: #1f2937; margin: 0 0 20px 0;">Reset Your Password</h2>
        
        <p style="color: #4b5563; margin: 0 0 20px 0;">
            We received a request to reset your password for your EasTask account. Click the button below to create a new password:
        </p>
        
        <div style="text-align: center; margin: 30px 0;">
            <a href="{{ .SiteURL }}/reset-password?access_token={{ .TokenHash }}&refresh_token={{ .RefreshTokenHash }}&type=recovery" 
               style="background: linear-gradient(135deg, #f97316 0%, #ea580c 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 8px; font-weight: bold; display: inline-block;">
                Reset Password
            </a>
        </div>
        
        <p style="color: #6b7280; font-size: 14px; margin: 20px 0 0 0;">
            This link will expire in {{ .ExpiresIn }} for security reasons.
        </p>
        
        <p style="color: #6b7280; font-size: 14px; margin: 10px 0 0 0;">
            If you can't click the button, copy and paste this link into your browser:
        </p>
        
        <p style="color: #6b7280; font-size: 14px; margin: 5px 0 0 0; word-break: break-all;">
            {{ .SiteURL }}/reset-password?access_token={{ .TokenHash }}&refresh_token={{ .RefreshTokenHash }}&type=recovery
        </p>
    </div>
    
    <div style="background: #f9fafb; padding: 20px; text-align: center; color: #6b7280; font-size: 12px; border-radius: 0 0 10px 10px;">
        <p style="margin: 0 0 10px 0;">If you didn't request a password reset, you can safely ignore this email.</p>
        <p style="margin: 0;">Your password will not be changed unless you click the link above and create a new one.</p>
    </div>
</body>
</html>
```

---

**🎯 The key fix is adding `refresh_token={{ .RefreshTokenHash }}` to the URL in your Supabase email template!**
