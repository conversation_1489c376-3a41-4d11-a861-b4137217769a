-- Test the accept_workspace_invitation function
-- Run this in Supabase SQL Editor to test the function

-- 1. Check if the function exists and its signature
SELECT 
  routine_name,
  routine_type,
  data_type,
  routine_definition
FROM information_schema.routines 
WHERE routine_name = 'accept_workspace_invitation';

-- 2. Check function parameters
SELECT 
  parameter_name,
  data_type,
  parameter_mode
FROM information_schema.parameters 
WHERE specific_name IN (
  SELECT specific_name 
  FROM information_schema.routines 
  WHERE routine_name = 'accept_workspace_invitation'
)
ORDER BY ordinal_position;

-- 3. Check if there are any pending invitations to test with
SELECT 
  id,
  workspace_name,
  invited_email,
  status,
  expires_at,
  created_at
FROM workspace_invitations 
WHERE status = 'pending'
ORDER BY created_at DESC
LIMIT 5;

-- 4. Check current user (this will show the authenticated user)
SELECT auth.uid() as current_user_id;
SELECT auth.email() as current_user_email;

-- If you want to test the function manually, uncomment and modify this:
-- Replace the UUIDs with actual values from the queries above
-- SELECT accept_workspace_invitation(
--   'your-invitation-id-here'::uuid,
--   auth.uid()
-- );
