Stack trace:
Frame         Function      Args
0007FFFF8E80  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF7D80) msys-2.0.dll+0x1FEBA
0007FFFF8E80  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFF9158) msys-2.0.dll+0x67F9
0007FFFF8E80  000210046832 (000210285FF9, 0007FFFF8D38, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF8E80  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF8E80  0002100690B4 (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFF9160  00021006A49D (0007FFFF8E90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFB8C960000 ntdll.dll
7FFB8BA80000 KERNEL32.DLL
7FFB89B70000 KERNELBASE.dll
7FFB8C670000 USER32.dll
7FFB89F60000 win32u.dll
7FFB8C080000 GDI32.dll
7FFB8A520000 gdi32full.dll
7FFB8A0E0000 msvcp_win.dll
7FFB89F90000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFB8C520000 advapi32.dll
7FFB8AEF0000 msvcrt.dll
7FFB8C0B0000 sechost.dll
7FFB8C400000 RPCRT4.dll
7FFB891D0000 CRYPTBASE.DLL
7FFB8A660000 bcryptPrimitives.dll
7FFB8A700000 IMM32.DLL
