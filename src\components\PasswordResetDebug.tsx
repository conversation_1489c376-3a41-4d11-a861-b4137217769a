import React from 'react';
import { useSearchParams } from 'react-router-dom';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from './ui/card';

const PasswordResetDebug: React.FC = () => {
  const [searchParams] = useSearchParams();
  
  const queryParams = Object.fromEntries(searchParams.entries());
  const hash = window.location.hash.substring(1);
  const hashParams = hash ? Object.fromEntries(new URLSearchParams(hash).entries()) : {};
  
  return (
    <Card className="w-full max-w-2xl mx-auto mt-8">
      <CardHeader>
        <CardTitle>Password Reset Debug Info</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <h3 className="font-semibold mb-2">Current URL:</h3>
          <p className="text-sm font-mono bg-gray-100 p-2 rounded break-all">
            {window.location.href}
          </p>
        </div>
        
        <div>
          <h3 className="font-semibold mb-2">Query Parameters:</h3>
          <pre className="text-sm bg-gray-100 p-2 rounded overflow-auto">
            {JSON.stringify(queryParams, null, 2)}
          </pre>
        </div>
        
        <div>
          <h3 className="font-semibold mb-2">Hash Parameters:</h3>
          <pre className="text-sm bg-gray-100 p-2 rounded overflow-auto">
            {JSON.stringify(hashParams, null, 2)}
          </pre>
        </div>
        
        <div>
          <h3 className="font-semibold mb-2">Required Parameters:</h3>
          <ul className="text-sm space-y-1">
            <li className={`flex items-center gap-2 ${queryParams.access_token || hashParams.access_token ? 'text-green-600' : 'text-red-600'}`}>
              <span className="w-2 h-2 rounded-full bg-current"></span>
              access_token: {queryParams.access_token || hashParams.access_token ? '✓ Present' : '✗ Missing'}
            </li>
            <li className={`flex items-center gap-2 ${queryParams.refresh_token || hashParams.refresh_token ? 'text-green-600' : 'text-red-600'}`}>
              <span className="w-2 h-2 rounded-full bg-current"></span>
              refresh_token: {queryParams.refresh_token || hashParams.refresh_token ? '✓ Present' : '✗ Missing'}
            </li>
            <li className={`flex items-center gap-2 ${(queryParams.type || hashParams.type) === 'recovery' ? 'text-green-600' : 'text-red-600'}`}>
              <span className="w-2 h-2 rounded-full bg-current"></span>
              type: {queryParams.type || hashParams.type || 'Missing'} {(queryParams.type || hashParams.type) === 'recovery' ? '✓' : '✗'}
            </li>
          </ul>
        </div>
        
        <div className="bg-blue-50 p-4 rounded">
          <h4 className="font-semibold text-blue-800 mb-2">Expected URL Format:</h4>
          <p className="text-sm text-blue-700 font-mono">
            /reset-password?access_token=TOKEN&refresh_token=TOKEN&type=recovery
          </p>
          <p className="text-xs text-blue-600 mt-2">
            This URL should be generated by Supabase when you click the reset link in your email.
          </p>
        </div>

        {/* Specific Issue Detection */}
        {(queryParams.refresh_token === "" || hashParams.refresh_token === "") && (
          <div className="bg-red-50 p-4 rounded border border-red-200">
            <h4 className="font-semibold text-red-800 mb-2">🚨 ISSUE DETECTED:</h4>
            <p className="text-sm text-red-700 mb-2">
              The refresh_token parameter is empty. This means your Supabase email template is missing the refresh token.
            </p>
            <div className="text-xs text-red-600">
              <p className="font-semibold mb-1">TO FIX:</p>
              <ol className="list-decimal list-inside space-y-1">
                <li>Go to Supabase Dashboard → Authentication → Email Templates</li>
                <li>Click "Password Recovery"</li>
                <li>Make sure the URL includes: &refresh_token={`{{ .RefreshTokenHash }}`}</li>
                <li>Save the template and request a NEW password reset</li>
              </ol>
            </div>
          </div>
        )}

        {(!queryParams.access_token && !hashParams.access_token) && (
          <div className="bg-yellow-50 p-4 rounded border border-yellow-200">
            <h4 className="font-semibold text-yellow-800 mb-2">⚠️ ACCESS TOKEN MISSING:</h4>
            <p className="text-sm text-yellow-700">
              No access token found. Make sure you're using the correct reset link from your email.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default PasswordResetDebug;
