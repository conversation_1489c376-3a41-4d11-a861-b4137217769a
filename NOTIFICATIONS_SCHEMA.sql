-- =====================================================
-- NOTIFICATIONS SCHEMA FOR EASTASK
-- =====================================================
-- Run this in Supabase SQL Editor to create notification tables

-- =====================================================
-- 1. NOTIFICATIONS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN (
    'invitation', 
    'task_assignment', 
    'task_update', 
    'workspace_update', 
    'mention', 
    'system',
    'task_comment',
    'task_status_change',
    'workspace_invitation'
  )),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  data JSONB DEFAULT '{}'::jsonb,
  read BOOLEAN DEFAULT FALSE,
  action_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 2. INDEXES FOR PERFORMANCE
-- =====================================================
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_workspace_id ON notifications(workspace_id);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read);
CREATE INDEX IF NOT EXISTS idx_notifications_type ON notifications(type);

-- Composite index for common queries
CREATE INDEX IF NOT EXISTS idx_notifications_user_read_created ON notifications(user_id, read, created_at DESC);

-- =====================================================
-- 3. ROW LEVEL SECURITY POLICIES
-- =====================================================
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Users can only see their own notifications
CREATE POLICY "Users can view their own notifications" ON notifications
  FOR SELECT USING (user_id = auth.uid());

-- Users can create notifications (for system use)
CREATE POLICY "Users can create notifications" ON notifications
  FOR INSERT WITH CHECK (user_id = auth.uid());

-- Users can update their own notifications (mark as read)
CREATE POLICY "Users can update their own notifications" ON notifications
  FOR UPDATE USING (user_id = auth.uid());

-- Users can delete their own notifications
CREATE POLICY "Users can delete their own notifications" ON notifications
  FOR DELETE USING (user_id = auth.uid());

-- =====================================================
-- 4. NOTIFICATION FUNCTIONS
-- =====================================================

-- Function to create a notification
CREATE OR REPLACE FUNCTION create_notification(
  p_user_id UUID,
  p_workspace_id UUID,
  p_type TEXT,
  p_title TEXT,
  p_message TEXT,
  p_data JSONB DEFAULT '{}'::jsonb,
  p_action_url TEXT DEFAULT NULL
)
RETURNS UUID AS $$
DECLARE
  notification_id UUID;
BEGIN
  INSERT INTO notifications (
    user_id, workspace_id, type, title, message, data, action_url
  )
  VALUES (
    p_user_id, p_workspace_id, p_type, p_title, p_message, p_data, p_action_url
  )
  RETURNING id INTO notification_id;
  
  RETURN notification_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark notification as read
CREATE OR REPLACE FUNCTION mark_notification_read(p_notification_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE notifications 
  SET read = TRUE 
  WHERE id = p_notification_id AND user_id = auth.uid();
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark all notifications as read for a user
CREATE OR REPLACE FUNCTION mark_all_notifications_read(p_user_id UUID DEFAULT NULL)
RETURNS INTEGER AS $$
DECLARE
  target_user_id UUID;
  updated_count INTEGER;
BEGIN
  target_user_id := COALESCE(p_user_id, auth.uid());
  
  UPDATE notifications 
  SET read = TRUE 
  WHERE user_id = target_user_id AND read = FALSE;
  
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  RETURN updated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get unread notification count
CREATE OR REPLACE FUNCTION get_unread_notification_count(p_user_id UUID DEFAULT NULL)
RETURNS INTEGER AS $$
DECLARE
  target_user_id UUID;
  unread_count INTEGER;
BEGIN
  target_user_id := COALESCE(p_user_id, auth.uid());
  
  SELECT COUNT(*) INTO unread_count
  FROM notifications 
  WHERE user_id = target_user_id AND read = FALSE;
  
  RETURN unread_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 5. NOTIFICATION TRIGGERS
-- =====================================================

-- Trigger to automatically clean up old notifications (keep last 1000 per user)
CREATE OR REPLACE FUNCTION cleanup_old_notifications()
RETURNS TRIGGER AS $$
BEGIN
  -- Keep only the latest 1000 notifications per user
  DELETE FROM notifications 
  WHERE user_id = NEW.user_id 
    AND id NOT IN (
      SELECT id FROM notifications 
      WHERE user_id = NEW.user_id 
      ORDER BY created_at DESC 
      LIMIT 1000
    );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER cleanup_notifications_trigger
  AFTER INSERT ON notifications
  FOR EACH ROW
  EXECUTE FUNCTION cleanup_old_notifications();

-- =====================================================
-- 6. REALTIME SUBSCRIPTIONS
-- =====================================================
-- Enable realtime for notifications table
ALTER PUBLICATION supabase_realtime ADD TABLE notifications;

-- =====================================================
-- 7. SAMPLE DATA (OPTIONAL - FOR TESTING)
-- =====================================================
-- Uncomment to create sample notifications for testing

/*
-- Sample notification for current user (if authenticated)
INSERT INTO notifications (
  user_id, 
  workspace_id, 
  type, 
  title, 
  message, 
  data,
  action_url
) VALUES (
  auth.uid(),
  (SELECT id FROM workspaces WHERE owner_id = auth.uid() LIMIT 1),
  'system',
  'Welcome to EasTask!',
  'Your notification system is now set up and working.',
  '{"welcome": true}'::jsonb,
  '/dashboard'
);
*/

-- =====================================================
-- 8. VERIFICATION QUERIES
-- =====================================================
-- Run these to verify the setup

-- Check if table exists
SELECT 'notifications table created' as status 
WHERE EXISTS (
  SELECT 1 FROM information_schema.tables 
  WHERE table_name = 'notifications' AND table_schema = 'public'
);

-- Check if RLS is enabled
SELECT 'RLS enabled on notifications' as status
WHERE EXISTS (
  SELECT 1 FROM pg_tables 
  WHERE tablename = 'notifications' AND rowsecurity = true
);

-- Check if functions exist
SELECT 'notification functions created' as status
WHERE EXISTS (
  SELECT 1 FROM information_schema.routines 
  WHERE routine_name = 'create_notification' AND routine_schema = 'public'
);

-- Success message
SELECT '✅ Notifications schema setup complete!' as result;
SELECT 'You can now use the notification system in EasTask.' as note;
