import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useSupabaseWorkspace } from '../contexts/SupabaseWorkspaceContext';
import { useSupabaseAuth } from '../contexts/SupabaseAuthContext';
import { notificationService } from '../services/notificationService';
import ModernLayout from '../components/ModernLayout';
import {
  Building2,
  Plus,
  Settings,
  Users,
  Mail,
  MoreVertical,
  Edit,
  Trash2,
  Crown,
  Shield,
  Eye,
  UserPlus,
  Copy,
  ExternalLink
} from 'lucide-react';
import { toast } from 'sonner';

const WorkspaceManagement: React.FC = () => {
  const navigate = useNavigate();
  const { user } = useSupabaseAuth();
  const { 
    userWorkspaces, 
    currentWorkspace, 
    workspaceMembers, 
    switchWorkspace,
    createWorkspace,
    updateWorkspace,
    deleteWorkspace,
    inviteMember,
    removeMember,
    updateMemberRole
  } = useSupabaseWorkspace();

  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showInviteDialog, setShowInviteDialog] = useState(false);
  const [selectedWorkspace, setSelectedWorkspace] = useState<any>(null);
  const [inviteEmail, setInviteEmail] = useState('');
  const [inviteRole, setInviteRole] = useState('member');

  const [newWorkspace, setNewWorkspace] = useState({
    name: '',
    description: ''
  });

  const [editWorkspace, setEditWorkspace] = useState({
    name: '',
    description: ''
  });

  const handleCreateWorkspace = async () => {
    if (!newWorkspace.name.trim()) {
      toast.error('Please enter a workspace name');
      return;
    }

    try {
      await createWorkspace(newWorkspace.name, newWorkspace.description);
      setNewWorkspace({ name: '', description: '' });
      setShowCreateDialog(false);
      toast.success('Workspace created successfully!');
    } catch (error) {
      console.error('Error creating workspace:', error);
      toast.error('Failed to create workspace');
    }
  };

  const handleEditWorkspace = async () => {
    if (!selectedWorkspace || !editWorkspace.name.trim()) {
      toast.error('Please enter a workspace name');
      return;
    }

    try {
      await updateWorkspace(selectedWorkspace.id, {
        name: editWorkspace.name,
        description: editWorkspace.description
      });
      setShowEditDialog(false);
      setSelectedWorkspace(null);
      toast.success('Workspace updated successfully!');
    } catch (error) {
      console.error('Error updating workspace:', error);
      toast.error('Failed to update workspace');
    }
  };

  const handleDeleteWorkspace = async (workspaceId: string) => {
    if (!confirm('Are you sure you want to delete this workspace? This action cannot be undone.')) {
      return;
    }

    try {
      await deleteWorkspace(workspaceId);
      toast.success('Workspace deleted successfully!');
    } catch (error) {
      console.error('Error deleting workspace:', error);
      toast.error('Failed to delete workspace');
    }
  };

  const handleInviteMember = async () => {
    if (!inviteEmail.trim() || !selectedWorkspace) {
      toast.error('Please enter an email address');
      return;
    }

    try {
      await inviteMember(selectedWorkspace.id, inviteEmail, inviteRole);
      
      // Send email notification
      if (user) {
        await notificationService.sendWorkspaceInvitation({
          invitedEmail: inviteEmail,
          inviterUserId: user.id,
          workspaceId: selectedWorkspace.id,
          workspaceName: selectedWorkspace.name,
          inviterName: user.user_metadata?.full_name || user.email || 'Unknown User',
          inviterEmail: user.email,
          role: inviteRole,
          inviteCode: selectedWorkspace.invite_code || `invite-${Date.now()}`
        });
      }

      setInviteEmail('');
      setShowInviteDialog(false);
      toast.success(`Invitation sent to ${inviteEmail}!`);
    } catch (error) {
      console.error('Error inviting member:', error);
      toast.error('Failed to send invitation');
    }
  };

  const handleRemoveMember = async (workspaceId: string, userId: string) => {
    if (!confirm('Are you sure you want to remove this member from the workspace?')) {
      return;
    }

    try {
      await removeMember(workspaceId, userId);
      toast.success('Member removed successfully!');
    } catch (error) {
      console.error('Error removing member:', error);
      toast.error('Failed to remove member');
    }
  };

  const handleRoleChange = async (workspaceId: string, userId: string, newRole: string) => {
    try {
      await updateMemberRole(workspaceId, userId, newRole);
      toast.success('Member role updated successfully!');
    } catch (error) {
      console.error('Error updating member role:', error);
      toast.error('Failed to update member role');
    }
  };

  const copyInviteLink = (inviteCode: string) => {
    const inviteLink = `${window.location.origin}/join/${inviteCode}`;
    navigator.clipboard.writeText(inviteLink);
    toast.success('Invite link copied to clipboard!');
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'owner': return <Crown className="h-4 w-4 text-yellow-500" />;
      case 'admin': return <Shield className="h-4 w-4 text-blue-500" />;
      case 'member': return <Users className="h-4 w-4 text-green-500" />;
      case 'viewer': return <Eye className="h-4 w-4 text-gray-500" />;
      default: return <Users className="h-4 w-4" />;
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'owner': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'admin': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'member': return 'bg-green-100 text-green-800 border-green-200';
      case 'viewer': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  return (
    <ModernLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Workspace Management</h1>
            <p className="text-muted-foreground">
              Manage your workspaces, members, and permissions
            </p>
          </div>
          <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
            <DialogTrigger asChild>
              <Button className="flex items-center gap-2">
                <Plus className="h-4 w-4" />
                Create Workspace
              </Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New Workspace</DialogTitle>
                <DialogDescription>
                  Create a new workspace to organize your projects and collaborate with your team.
                </DialogDescription>
              </DialogHeader>
              <div className="space-y-4">
                <div>
                  <Label htmlFor="workspace-name">Workspace Name</Label>
                  <Input
                    id="workspace-name"
                    placeholder="Enter workspace name"
                    value={newWorkspace.name}
                    onChange={(e) => setNewWorkspace({ ...newWorkspace, name: e.target.value })}
                  />
                </div>
                <div>
                  <Label htmlFor="workspace-description">Description (Optional)</Label>
                  <Textarea
                    id="workspace-description"
                    placeholder="Enter workspace description"
                    value={newWorkspace.description}
                    onChange={(e) => setNewWorkspace({ ...newWorkspace, description: e.target.value })}
                    rows={3}
                  />
                </div>
                <div className="flex gap-2 justify-end">
                  <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleCreateWorkspace}>
                    Create Workspace
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {/* Current Workspace */}
        {currentWorkspace && (
          <Card className="border-primary/20 bg-primary/5">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5 text-primary" />
                Current Workspace
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">{currentWorkspace.name}</h3>
                  <p className="text-sm text-muted-foreground">
                    {currentWorkspace.description || 'No description'}
                  </p>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant="secondary" className="flex items-center gap-1">
                      <Users className="h-3 w-3" />
                      {workspaceMembers.length} members
                    </Badge>
                  </div>
                </div>
                <Button
                  variant="outline"
                  onClick={() => {
                    setSelectedWorkspace(currentWorkspace);
                    setShowInviteDialog(true);
                  }}
                  className="flex items-center gap-2"
                >
                  <UserPlus className="h-4 w-4" />
                  Invite Members
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* All Workspaces */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {userWorkspaces.map((workspace) => (
            <Card key={workspace.id} className={`relative ${
              currentWorkspace?.id === workspace.id ? 'ring-2 ring-primary' : ''
            }`}>
              <CardHeader className="pb-3">
                <div className="flex items-start justify-between">
                  <div className="flex items-center gap-2">
                    <Building2 className="h-5 w-5 text-primary" />
                    <CardTitle className="text-lg">{workspace.name}</CardTitle>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm">
                        <MoreVertical className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => switchWorkspace(workspace.id)}
                        disabled={currentWorkspace?.id === workspace.id}
                      >
                        <Building2 className="h-4 w-4 mr-2" />
                        Switch to Workspace
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => {
                          setSelectedWorkspace(workspace);
                          setEditWorkspace({
                            name: workspace.name,
                            description: workspace.description || ''
                          });
                          setShowEditDialog(true);
                        }}
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        Edit Workspace
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => {
                          setSelectedWorkspace(workspace);
                          setShowInviteDialog(true);
                        }}
                      >
                        <UserPlus className="h-4 w-4 mr-2" />
                        Invite Members
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => copyInviteLink(workspace.invite_code || `invite-${workspace.id}`)}
                      >
                        <Copy className="h-4 w-4 mr-2" />
                        Copy Invite Link
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={() => handleDeleteWorkspace(workspace.id)}
                        className="text-red-600"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete Workspace
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <CardDescription>
                  {workspace.description || 'No description'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Members</span>
                    <Badge variant="secondary">
                      {workspace.member_count || 0}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-muted-foreground">Created</span>
                    <span>{new Date(workspace.created_at).toLocaleDateString()}</span>
                  </div>
                  {currentWorkspace?.id === workspace.id && (
                    <Badge className="w-full justify-center">
                      Current Workspace
                    </Badge>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Workspace Members */}
        {currentWorkspace && workspaceMembers.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                Workspace Members
              </CardTitle>
              <CardDescription>
                Manage members and their roles in {currentWorkspace.name}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {workspaceMembers.map((member) => (
                  <div key={member.userId} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={member.photoURL} />
                        <AvatarFallback className="bg-gradient-to-br from-orange-500 to-orange-600 text-white">
                          {member.displayName.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium">{member.displayName}</div>
                        <div className="text-sm text-muted-foreground">{member.email}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge className={`flex items-center gap-1 ${getRoleBadgeColor(member.role)}`}>
                        {getRoleIcon(member.role)}
                        {member.role}
                      </Badge>
                      {member.role !== 'owner' && (
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Change Role</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleRoleChange(currentWorkspace.id, member.userId, 'admin')}
                              disabled={member.role === 'admin'}
                            >
                              <Shield className="h-4 w-4 mr-2" />
                              Admin
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleRoleChange(currentWorkspace.id, member.userId, 'member')}
                              disabled={member.role === 'member'}
                            >
                              <Users className="h-4 w-4 mr-2" />
                              Member
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => handleRoleChange(currentWorkspace.id, member.userId, 'viewer')}
                              disabled={member.role === 'viewer'}
                            >
                              <Eye className="h-4 w-4 mr-2" />
                              Viewer
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => handleRemoveMember(currentWorkspace.id, member.userId)}
                              className="text-red-600"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Remove Member
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Edit Workspace Dialog */}
        <Dialog open={showEditDialog} onOpenChange={setShowEditDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Edit Workspace</DialogTitle>
              <DialogDescription>
                Update your workspace information.
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="edit-workspace-name">Workspace Name</Label>
                <Input
                  id="edit-workspace-name"
                  placeholder="Enter workspace name"
                  value={editWorkspace.name}
                  onChange={(e) => setEditWorkspace({ ...editWorkspace, name: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="edit-workspace-description">Description (Optional)</Label>
                <Textarea
                  id="edit-workspace-description"
                  placeholder="Enter workspace description"
                  value={editWorkspace.description}
                  onChange={(e) => setEditWorkspace({ ...editWorkspace, description: e.target.value })}
                  rows={3}
                />
              </div>
              <div className="flex gap-2 justify-end">
                <Button variant="outline" onClick={() => setShowEditDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleEditWorkspace}>
                  Update Workspace
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>

        {/* Invite Member Dialog */}
        <Dialog open={showInviteDialog} onOpenChange={setShowInviteDialog}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Invite Member</DialogTitle>
              <DialogDescription>
                Invite a new member to {selectedWorkspace?.name}
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="invite-email">Email Address</Label>
                <Input
                  id="invite-email"
                  type="email"
                  placeholder="Enter email address"
                  value={inviteEmail}
                  onChange={(e) => setInviteEmail(e.target.value)}
                />
              </div>
              <div>
                <Label htmlFor="invite-role">Role</Label>
                <select
                  id="invite-role"
                  className="w-full p-2 border rounded-md"
                  value={inviteRole}
                  onChange={(e) => setInviteRole(e.target.value)}
                >
                  <option value="viewer">Viewer</option>
                  <option value="member">Member</option>
                  <option value="admin">Admin</option>
                </select>
              </div>
              <div className="flex gap-2 justify-end">
                <Button variant="outline" onClick={() => setShowInviteDialog(false)}>
                  Cancel
                </Button>
                <Button onClick={handleInviteMember}>
                  Send Invitation
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>
    </ModernLayout>
  );
};

export default WorkspaceManagement;
