-- =====================================================
-- DEBUG: Check if EasTask Profile Tables Exist
-- =====================================================
-- Run this in Supabase SQL Editor to check current state

-- Check if profile tables exist
SELECT 'CHECKING TABLES' as status;

SELECT 
  table_name,
  'EXISTS' as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name IN ('user_profiles', 'user_settings', 'user_activity', 'user_statistics')
ORDER BY table_name;

-- Check if functions exist
SELECT 'CHECKING FUNCTIONS' as status;

SELECT 
  routine_name,
  routine_type,
  'EXISTS' as status
FROM information_schema.routines 
WHERE routine_schema = 'public' 
  AND routine_name IN ('create_user_profile_and_settings', 'log_user_activity', 'update_user_statistics')
ORDER BY routine_name;

-- Check current user
SELECT 'CURRENT USER' as status;
SELECT auth.uid() as current_user_id, auth.email() as current_user_email;

-- Check if current user has profile
SELECT 'USER PROFILE CHECK' as status;
SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM user_profiles WHERE user_id = auth.uid()) 
    THEN 'PROFILE EXISTS' 
    ELSE 'NO PROFILE FOUND' 
  END as profile_status;

-- Check if current user has settings
SELECT 'USER SETTINGS CHECK' as status;
SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM user_settings WHERE user_id = auth.uid()) 
    THEN 'SETTINGS EXIST' 
    ELSE 'NO SETTINGS FOUND' 
  END as settings_status;
