-- =====================================================
-- FIX ACCEPT WORKSPACE INVITATION FUNCTION V2
-- =====================================================
-- This fixes the "column reference user_id is ambiguous" error
-- by renaming the function parameter to avoid conflicts

-- Drop existing functions completely
DROP FUNCTION IF EXISTS accept_workspace_invitation(UUID, UUID);
DROP FUNCTION IF EXISTS decline_workspace_invitation(UUID, UUID);

-- Create the fixed function with renamed parameters
CREATE OR REPLACE FUNCTION accept_workspace_invitation(
  invitation_id UUID,
  accepting_user_id UUID  -- Renamed to avoid ambiguity
) RETURNS BOOLEAN AS $$
DECLARE
  invitation_record workspace_invitations%ROWTYPE;
  user_email TEXT;
  existing_member_count INTEGER;
BEGIN
  -- Get user email
  SELECT email INTO user_email 
  FROM auth.users 
  WHERE id = accepting_user_id;
  
  IF user_email IS NULL THEN
    RAISE EXCEPTION 'User not found';
  END IF;

  -- Get the invitation with proper alias
  SELECT * INTO invitation_record 
  FROM workspace_invitations wi
  WHERE wi.id = invitation_id 
  AND wi.status = 'pending' 
  AND wi.expires_at > NOW()
  AND wi.invited_email = user_email;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Invalid invitation or not authorized';
  END IF;

  -- Check if user is already a member with proper alias
  SELECT COUNT(*) INTO existing_member_count
  FROM workspace_members wm
  WHERE wm.workspace_id = invitation_record.workspace_id 
  AND wm.user_id = accepting_user_id;  -- Now unambiguous
  
  IF existing_member_count > 0 THEN
    RAISE EXCEPTION 'User is already a member of this workspace';
  END IF;

  -- Add user as workspace member
  INSERT INTO workspace_members (
    workspace_id,
    user_id,
    role,
    joined_at,
    invited_by
  ) VALUES (
    invitation_record.workspace_id,
    accepting_user_id,  -- Now unambiguous
    invitation_record.role::user_role,  -- Cast to correct type
    NOW(),
    invitation_record.invited_by
  );
  
  -- Update invitation status
  UPDATE workspace_invitations 
  SET status = 'accepted', updated_at = NOW()
  WHERE id = invitation_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create the fixed decline function with renamed parameters
CREATE OR REPLACE FUNCTION decline_workspace_invitation(
  invitation_id UUID,
  declining_user_id UUID  -- Renamed to avoid ambiguity
) RETURNS VOID AS $$
DECLARE
  invitation_record workspace_invitations%ROWTYPE;
  user_email TEXT;
BEGIN
  -- Get user email
  SELECT email INTO user_email 
  FROM auth.users 
  WHERE id = declining_user_id;
  
  IF user_email IS NULL THEN
    RAISE EXCEPTION 'User not found';
  END IF;

  -- Get the invitation details with proper alias
  SELECT * INTO invitation_record
  FROM workspace_invitations wi
  WHERE wi.id = invitation_id
    AND wi.status = 'pending'
    AND wi.invited_email = user_email;

  -- Check if invitation exists and belongs to user
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Invalid invitation or not authorized';
  END IF;

  -- Update invitation status
  UPDATE workspace_invitations
  SET status = 'declined', updated_at = NOW()
  WHERE id = invitation_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant permissions
GRANT EXECUTE ON FUNCTION accept_workspace_invitation(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION decline_workspace_invitation(UUID, UUID) TO authenticated;

-- Test the function
DO $$
BEGIN
  RAISE NOTICE 'Functions recreated successfully with renamed parameters!';
  RAISE NOTICE 'The ambiguous column reference issue should now be completely fixed.';
END $$;

-- Verify the functions exist
SELECT 
  routine_name,
  'recreated with fixed parameters' as status
FROM information_schema.routines 
WHERE routine_name IN ('accept_workspace_invitation', 'decline_workspace_invitation')
ORDER BY routine_name;
