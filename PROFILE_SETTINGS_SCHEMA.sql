-- =====================================================
-- EASTASK PROFILE & SETTINGS DATABASE SCHEMA
-- =====================================================
-- This script creates the database schema for user profiles and settings

-- STEP 1: Create user_profiles table
CREATE TABLE IF NOT EXISTS user_profiles (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  full_name TEXT,
  phone TEXT,
  location TEXT,
  job_title TEXT,
  department TEXT,
  employee_id TEXT,
  bio TEXT,
  timezone TEXT DEFAULT 'UTC',
  pronouns TEXT,
  start_date DATE,
  manager TEXT,
  skills TEXT[] DEFAULT '{}',
  profile_image_url TEXT,
  working_hours_start TIME DEFAULT '09:00:00',
  working_hours_end TIME DEFAULT '17:00:00',
  preferred_communication TEXT DEFAULT 'email',
  goals_objectives TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id),
  UNIQUE(employee_id)
);

-- STEP 2: Create user_settings table
CREATE TABLE IF NOT EXISTS user_settings (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  
  -- Appearance Settings
  theme TEXT DEFAULT 'system' CHECK (theme IN ('light', 'dark', 'system')),
  language TEXT DEFAULT 'en' CHECK (language IN ('en', 'es', 'fr', 'de', 'ja')),
  timezone TEXT DEFAULT 'UTC',
  date_format TEXT DEFAULT 'mm/dd/yyyy' CHECK (date_format IN ('mm/dd/yyyy', 'dd/mm/yyyy', 'yyyy-mm-dd')),
  time_format TEXT DEFAULT '12' CHECK (time_format IN ('12', '24')),
  first_day_of_week TEXT DEFAULT 'sunday' CHECK (first_day_of_week IN ('sunday', 'monday')),
  currency TEXT DEFAULT 'usd' CHECK (currency IN ('usd', 'eur', 'gbp', 'inr')),
  
  -- Notification Settings
  email_notifications BOOLEAN DEFAULT true,
  push_notifications BOOLEAN DEFAULT true,
  task_reminders BOOLEAN DEFAULT true,
  project_updates BOOLEAN DEFAULT true,
  weekly_digest BOOLEAN DEFAULT false,
  marketing_emails BOOLEAN DEFAULT false,
  
  -- Privacy Settings
  profile_visibility TEXT DEFAULT 'team' CHECK (profile_visibility IN ('public', 'team', 'private')),
  activity_status BOOLEAN DEFAULT true,
  data_sharing BOOLEAN DEFAULT false,
  analytics_tracking BOOLEAN DEFAULT true,
  
  -- Security Settings
  two_factor_auth BOOLEAN DEFAULT false,
  session_timeout INTEGER DEFAULT 30, -- minutes
  login_alerts BOOLEAN DEFAULT true,
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- STEP 3: Create user_activity table for tracking user actions
CREATE TABLE IF NOT EXISTS user_activity (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  activity_type TEXT NOT NULL,
  activity_description TEXT NOT NULL,
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- STEP 4: Create user_statistics table for performance metrics
CREATE TABLE IF NOT EXISTS user_statistics (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  tasks_completed INTEGER DEFAULT 0,
  projects_count INTEGER DEFAULT 0,
  hours_logged INTEGER DEFAULT 0,
  on_time_rate DECIMAL(5,2) DEFAULT 0.00,
  last_calculated TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- STEP 5: Create functions for automatic profile/settings creation
CREATE OR REPLACE FUNCTION create_user_profile_and_settings()
RETURNS TRIGGER AS $$
BEGIN
  -- Create user profile
  INSERT INTO user_profiles (user_id, full_name)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email)
  )
  ON CONFLICT (user_id) DO NOTHING;
  
  -- Create user settings with defaults
  INSERT INTO user_settings (user_id)
  VALUES (NEW.id)
  ON CONFLICT (user_id) DO NOTHING;
  
  -- Create user statistics
  INSERT INTO user_statistics (user_id)
  VALUES (NEW.id)
  ON CONFLICT (user_id) DO NOTHING;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- STEP 6: Create trigger for automatic profile creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION create_user_profile_and_settings();

-- STEP 7: Create function to update user activity
CREATE OR REPLACE FUNCTION log_user_activity(
  p_user_id UUID,
  p_activity_type TEXT,
  p_activity_description TEXT,
  p_metadata JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
  activity_id UUID;
BEGIN
  INSERT INTO user_activity (user_id, activity_type, activity_description, metadata)
  VALUES (p_user_id, p_activity_type, p_activity_description, p_metadata)
  RETURNING id INTO activity_id;
  
  RETURN activity_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- STEP 8: Create function to update user statistics
CREATE OR REPLACE FUNCTION update_user_statistics(p_user_id UUID)
RETURNS VOID AS $$
DECLARE
  task_count INTEGER;
  project_count INTEGER;
  hours_count INTEGER;
  ontime_rate DECIMAL(5,2);
BEGIN
  -- Calculate tasks completed (you'll need to adjust based on your tasks table)
  SELECT COUNT(*) INTO task_count
  FROM tasks 
  WHERE assigned_to = p_user_id AND status = 'completed';
  
  -- Calculate projects (you'll need to adjust based on your projects table)
  SELECT COUNT(DISTINCT project_id) INTO project_count
  FROM tasks 
  WHERE assigned_to = p_user_id;
  
  -- Calculate hours logged (placeholder - adjust based on your time tracking)
  hours_count := task_count * 2; -- Placeholder calculation
  
  -- Calculate on-time rate (placeholder - adjust based on your deadline tracking)
  ontime_rate := 92.0; -- Placeholder value
  
  -- Update or insert statistics
  INSERT INTO user_statistics (
    user_id, 
    tasks_completed, 
    projects_count, 
    hours_logged, 
    on_time_rate,
    last_calculated,
    updated_at
  )
  VALUES (
    p_user_id, 
    task_count, 
    project_count, 
    hours_count, 
    ontime_rate,
    NOW(),
    NOW()
  )
  ON CONFLICT (user_id) 
  DO UPDATE SET
    tasks_completed = EXCLUDED.tasks_completed,
    projects_count = EXCLUDED.projects_count,
    hours_logged = EXCLUDED.hours_logged,
    on_time_rate = EXCLUDED.on_time_rate,
    last_calculated = NOW(),
    updated_at = NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- STEP 9: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_user_profiles_user_id ON user_profiles(user_id);
CREATE INDEX IF NOT EXISTS idx_user_profiles_employee_id ON user_profiles(employee_id);
CREATE INDEX IF NOT EXISTS idx_user_settings_user_id ON user_settings(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_user_id ON user_activity(user_id);
CREATE INDEX IF NOT EXISTS idx_user_activity_created_at ON user_activity(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_user_statistics_user_id ON user_statistics(user_id);

-- STEP 10: Set up RLS policies
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_activity ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_statistics ENABLE ROW LEVEL SECURITY;

-- Policies for user_profiles
CREATE POLICY "Users can view their own profile" ON user_profiles
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can update their own profile" ON user_profiles
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own profile" ON user_profiles
  FOR INSERT WITH CHECK (user_id = auth.uid());

-- Team members can view profiles in same workspace
CREATE POLICY "Team members can view profiles" ON user_profiles
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM workspace_members wm1
      JOIN workspace_members wm2 ON wm1.workspace_id = wm2.workspace_id
      WHERE wm1.user_id = auth.uid() AND wm2.user_id = user_profiles.user_id
    )
  );

-- Policies for user_settings
CREATE POLICY "Users can manage their own settings" ON user_settings
  FOR ALL USING (user_id = auth.uid());

-- Policies for user_activity
CREATE POLICY "Users can view their own activity" ON user_activity
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "System can insert activity" ON user_activity
  FOR INSERT WITH CHECK (true);

-- Policies for user_statistics
CREATE POLICY "Users can view their own statistics" ON user_statistics
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "System can manage statistics" ON user_statistics
  FOR ALL USING (true);

-- STEP 11: Grant permissions
GRANT EXECUTE ON FUNCTION create_user_profile_and_settings() TO authenticated;
GRANT EXECUTE ON FUNCTION log_user_activity(UUID, TEXT, TEXT, JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION update_user_statistics(UUID) TO authenticated;

-- STEP 12: Create initial profiles for existing users
INSERT INTO user_profiles (user_id, full_name)
SELECT 
  id,
  COALESCE(raw_user_meta_data->>'full_name', email)
FROM auth.users
ON CONFLICT (user_id) DO NOTHING;

INSERT INTO user_settings (user_id)
SELECT id FROM auth.users
ON CONFLICT (user_id) DO NOTHING;

INSERT INTO user_statistics (user_id)
SELECT id FROM auth.users
ON CONFLICT (user_id) DO NOTHING;

-- Success message
DO $$
BEGIN
  RAISE NOTICE 'EasTask Profile & Settings schema created successfully!';
  RAISE NOTICE 'Tables created: user_profiles, user_settings, user_activity, user_statistics';
  RAISE NOTICE 'Functions created: create_user_profile_and_settings, log_user_activity, update_user_statistics';
  RAISE NOTICE 'RLS policies created for security';
  RAISE NOTICE 'Indexes created for better performance';
END $$;
