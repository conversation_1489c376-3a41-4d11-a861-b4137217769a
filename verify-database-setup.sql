-- =====================================================
-- VERIFY DATABASE SETUP
-- =====================================================
-- Run this in Supabase SQL Editor to check if everything is set up correctly

-- 1. Check if workspace_invitations table exists
SELECT 'Checking workspace_invitations table...' as step;
SELECT 
  table_name,
  'exists' as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name = 'workspace_invitations';

-- 2. Check table structure
SELECT 'Checking table structure...' as step;
SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'workspace_invitations'
AND table_schema = 'public'
ORDER BY ordinal_position;

-- 3. Check if functions exist
SELECT 'Checking functions...' as step;
SELECT 
  routine_name,
  routine_type,
  'exists' as status
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name LIKE '%invitation%'
ORDER BY routine_name;

-- 4. Check RLS policies
SELECT 'Checking RLS policies...' as step;
SELECT 
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies 
WHERE tablename = 'workspace_invitations';

-- 5. Test basic table access (should work if RLS is set up correctly)
SELECT 'Testing table access...' as step;
SELECT COUNT(*) as invitation_count FROM workspace_invitations;

-- 6. Check if workspaces table exists (dependency)
SELECT 'Checking workspaces table...' as step;
SELECT 
  table_name,
  'exists' as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name = 'workspaces';

-- 7. Test function call (this might fail but should give us info)
SELECT 'Testing function call...' as step;
-- This will likely fail but tells us if the function exists
-- SELECT accept_workspace_invitation('00000000-0000-0000-0000-000000000000', '00000000-0000-0000-0000-000000000000');

-- Success message
SELECT '✅ Database verification complete!' as result;
SELECT 'If you see results for all checks above, the database is set up correctly.' as note;
