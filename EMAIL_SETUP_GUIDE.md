# 📧 EasTask Email Service Setup Guide

## 🏆 Recommended Email Provider: RESEND

**Why Resend?**
- ✅ **3,000 free emails/month** (100/day)
- ✅ **Developer-friendly** API
- ✅ **Easy domain verification**
- ✅ **Excellent deliverability**
- ✅ **Already integrated** in your code

---

## 🚀 Step-by-Step Setup

### Step 1: Create Resend Account

1. **Visit**: https://resend.com
2. **Sign up** with your email
3. **Verify** your email address
4. **Complete** the onboarding process

### Step 2: Get Your API Key

1. **Login** to Resend dashboard
2. Go to **"API Keys"** section
3. Click **"Create API Key"**
4. **Name**: `EasTask Production` (or `EasTask Development`)
5. **Copy** the API key (starts with `re_`)
6. **Save it securely** - you'll need it for Step 4

### Step 3: Domain Setup (Optional but Recommended)

**Option A: Use Your Own Domain (Recommended)**
1. Go to **"Domains"** in Resend dashboard
2. Click **"Add Domain"**
3. Enter your domain: `yourdomain.com`
4. **Add DNS records** as shown in dashboard:
   - TXT record for domain verification
   - MX records for email routing
5. **Wait for verification** (5-30 minutes)
6. **Use**: `<EMAIL>` as sender

**Option B: Use Resend's Domain (Quick Start)**
- **Use**: `<EMAIL>` as sender
- **No setup required** - works immediately
- **Limited branding** but functional

### Step 4: Configure Environment Variables

**Edit your `.env.local` file:**

```bash
# =====================================================
# EMAIL SERVICE CONFIGURATION
# =====================================================

# Change from 'demo' to 'resend'
VITE_EMAIL_SERVICE_PROVIDER=resend

# Add your Resend API key (replace with actual key)
VITE_EMAIL_SERVICE_API_KEY=re_xxxxxxxxxxxxxxxxxxxxxxxxxx

# Set your sender email
VITE_FROM_EMAIL=<EMAIL>
# OR use Resend's domain: <EMAIL>

# Set sender name
VITE_FROM_NAME=EasTask Team

# Your app URL (for email links)
VITE_APP_URL=http://localhost:8081
```

### Step 5: Test Email Service

1. **Restart** your development server:
   ```bash
   npm run dev
   ```

2. **Login** to your EasTask application

3. **Go to Settings** → **Email Tab**

4. **Enter your email** in the test field

5. **Click "Test Invitation Email"** or **"Test Task Assignment"**

6. **Check your inbox** for the test email

---

## 🔧 Alternative Providers

### SendGrid (Alternative)

**Free Tier**: 100 emails/day

**Setup**:
1. Sign up at https://sendgrid.com
2. Get API key from dashboard
3. Verify sender email
4. Update `.env.local`:
   ```bash
   VITE_EMAIL_SERVICE_PROVIDER=sendgrid
   VITE_EMAIL_SERVICE_API_KEY=SG.xxxxxxxxxxxxxxxxxx
   VITE_FROM_EMAIL=<EMAIL>
   ```

### Mailgun (Backup)

**Free Tier**: 5,000 emails/month (3 months)

**Setup**:
1. Sign up at https://mailgun.com
2. Add domain and verify
3. Get API key
4. Update `.env.local`:
   ```bash
   VITE_EMAIL_SERVICE_PROVIDER=mailgun
   VITE_EMAIL_SERVICE_API_KEY=key-xxxxxxxxxxxxxxxxxx
   VITE_FROM_EMAIL=<EMAIL>
   ```

---

## 📧 Email Features in EasTask

### Workspace Invitations
- **Sent when**: Inviting users to workspace
- **Contains**: Invitation link, workspace details
- **Template**: Professional branded design

### Task Assignments
- **Sent when**: Assigning tasks to team members
- **Contains**: Task details, due date, priority
- **Template**: Task-focused with action buttons

### Task Reminders
- **Sent when**: Tasks are approaching due date
- **Contains**: Task details, urgency indicators
- **Template**: Reminder-focused design

---

## 🛠️ Troubleshooting

### Email Not Sending
1. **Check API key** is correct in `.env.local`
2. **Verify provider** is set to 'resend'
3. **Check sender email** is verified
4. **Restart development server**

### Emails Going to Spam
1. **Verify your domain** with email provider
2. **Add SPF/DKIM records** as instructed
3. **Use professional sender name**
4. **Avoid spam trigger words**

### API Key Issues
1. **Regenerate API key** in provider dashboard
2. **Check permissions** on the key
3. **Ensure no extra spaces** in `.env.local`

### Domain Verification Failed
1. **Wait 30 minutes** after adding DNS records
2. **Check DNS propagation** with online tools
3. **Verify exact record values** match dashboard
4. **Contact provider support** if issues persist

---

## 🎯 Production Deployment

### Environment Variables for Production

**Netlify/Vercel**:
```bash
VITE_EMAIL_SERVICE_PROVIDER=resend
VITE_EMAIL_SERVICE_API_KEY=re_your_production_key
VITE_FROM_EMAIL=<EMAIL>
VITE_FROM_NAME=EasTask Team
VITE_APP_URL=https://yourdomain.com
```

### Security Best Practices
1. **Use different API keys** for development/production
2. **Restrict API key permissions** if possible
3. **Monitor email usage** in provider dashboard
4. **Set up alerts** for quota limits
5. **Regularly rotate API keys**

---

## 📊 Monitoring & Analytics

### Resend Dashboard
- **Email delivery status**
- **Open/click rates**
- **Bounce/complaint rates**
- **Usage statistics**

### EasTask Logs
- **Check browser console** for email service logs
- **Monitor success/failure rates**
- **Track email types sent**

---

## 🆘 Support

### Resend Support
- **Documentation**: https://resend.com/docs
- **Support**: https://resend.com/support
- **Status**: https://status.resend.com

### EasTask Email Issues
- **Check Settings → Email tab** for configuration
- **Test with demo mode** first
- **Verify environment variables**
- **Check browser console** for errors

---

**🎉 Congratulations! Your email service is now configured and ready to send professional notifications to your team members!**
