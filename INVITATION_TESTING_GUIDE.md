# Invitation System Testing Guide

## Issues Fixed

### 1. Database Schema Issues
- ✅ Fixed column name mismatch: `invited_email` → `invitee_email`
- ✅ Added missing `invite_code` column handling
- ✅ Fixed database query joins (removed non-existent `profiles` table)

### 2. Database Functions
- ✅ Created `accept_workspace_invitation()` function
- ✅ Created `decline_workspace_invitation()` function
- ✅ Added proper error handling and validation

### 3. Email Service Integration
- ✅ Fixed invite code generation and usage
- ✅ Corrected email service parameters

## Required Database Setup

**IMPORTANT**: Run this SQL in your Supabase SQL editor first:

```sql
-- Run the INVITATION_SYSTEM_FIX.sql file
-- This creates the necessary functions and fixes the schema
```

## Testing Steps

### Step 1: Verify Database Setup
1. Go to Supabase Dashboard → SQL Editor
2. Run the `INVITATION_SYSTEM_FIX.sql` file
3. Verify no errors in the output

### Step 2: Test Invitation Creation
1. Open the app at `http://localhost:8081`
2. Login with a user account
3. Create or select a workspace
4. Click on the workspace selector in the navbar
5. Click "Manage Members"
6. Try inviting a user by email:
   - Click "Invite by Email"
   - Enter an email address
   - Select a role (member/admin)
   - Click "Send Invitation"

**Expected Result**: 
- Success toast message
- Email sent (or demo message if no email service configured)
- Invitation record created in database

### Step 3: Test Invitation Display
1. Login with the invited user's email
2. Go to the Home page
3. Look for the "Workspace Invitations" section

**Expected Result**:
- Invitation should appear in the InvitationManager component
- Shows workspace name, inviter name, role, and creation date
- Accept/Decline buttons should be visible

### Step 4: Test Invitation Acceptance
1. Click "Accept" on a pending invitation
2. Check for success message
3. Verify user is added to workspace

**Expected Result**:
- Success toast: "Successfully joined [workspace name]!"
- User becomes a member of the workspace
- Invitation status changes to 'accepted'

### Step 5: Test Invitation Decline
1. Click "Decline" on a pending invitation
2. Check for success message

**Expected Result**:
- Success toast: "Invitation declined"
- Invitation status changes to 'declined'
- Invitation disappears from list

## Debugging

### Check Database Records
```sql
-- Check invitations
SELECT * FROM workspace_invitations ORDER BY created_at DESC;

-- Check workspace members
SELECT * FROM workspace_members ORDER BY joined_at DESC;

-- Check user activities
SELECT * FROM user_activities WHERE activity_type = 'member_invited' ORDER BY timestamp DESC;
```

### Check Browser Console
1. Open Developer Tools (F12)
2. Look for any JavaScript errors
3. Check Network tab for failed API calls

### Common Issues

#### 1. No Invitations Showing
- Check if user email matches `invitee_email` in database
- Verify invitation status is 'pending'
- Check if invitation has expired

#### 2. Accept/Decline Not Working
- Verify database functions exist
- Check browser console for errors
- Ensure user has proper permissions

#### 3. Email Not Sending
- Check if `VITE_EMAIL_SERVICE_API_KEY` is set
- Verify email service configuration
- In demo mode, emails are simulated (check console)

## Email Service Configuration (Optional)

To enable real email sending, set these environment variables:

```env
VITE_EMAIL_SERVICE_API_KEY=your_api_key
VITE_EMAIL_SERVICE_PROVIDER=resend
VITE_FROM_EMAIL=<EMAIL>
```

Supported providers:
- `resend` (recommended)
- `sendgrid`
- `mailgun`

## Success Criteria

✅ **Invitation Creation**: Users can invite others by email
✅ **Invitation Display**: Invited users see pending invitations
✅ **Invitation Acceptance**: Users can accept invitations and join workspaces
✅ **Invitation Decline**: Users can decline invitations
✅ **Email Notifications**: Emails are sent (or simulated in demo mode)
✅ **Database Consistency**: All operations update database correctly

## Next Steps

After verifying the invitation system works:
1. Test with multiple users
2. Test edge cases (expired invitations, duplicate invites)
3. Verify email templates look good
4. Test workspace switching after joining
