import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { supabase } from '../lib/supabase';
import { useSupabaseAuth } from '../contexts/SupabaseAuthContext';
import { CheckCircle, XCircle, Database, Loader2 } from 'lucide-react';
import { toast } from 'sonner';

const DatabaseTest: React.FC = () => {
  const { user } = useSupabaseAuth();
  const [isChecking, setIsChecking] = useState(false);
  const [dbStatus, setDbStatus] = useState<{
    notifications: boolean;
    workspaces: boolean;
    tasks: boolean;
    user_activity: boolean;
    functions: boolean;
  } | null>(null);

  const checkDatabase = async () => {
    setIsChecking(true);
    try {
      const status = {
        notifications: false,
        workspaces: false,
        tasks: false,
        user_activity: false,
        functions: false
      };

      // Check if notifications table exists
      try {
        const { error: notifError } = await supabase
          .from('notifications')
          .select('id')
          .limit(1);
        status.notifications = !notifError;
      } catch (e) {
        console.log('Notifications table check:', e);
      }

      // Check if workspaces table exists
      try {
        const { error: workspaceError } = await supabase
          .from('workspaces')
          .select('id')
          .limit(1);
        status.workspaces = !workspaceError;
      } catch (e) {
        console.log('Workspaces table check:', e);
      }

      // Check if tasks table exists
      try {
        const { error: tasksError } = await supabase
          .from('tasks')
          .select('id')
          .limit(1);
        status.tasks = !tasksError;
      } catch (e) {
        console.log('Tasks table check:', e);
      }

      // Check if user_activity table exists
      try {
        const { error: activityError } = await supabase
          .from('user_activity')
          .select('id')
          .limit(1);
        status.user_activity = !activityError;
      } catch (e) {
        console.log('User activity table check:', e);
      }

      // Check if notification functions exist (try to call one)
      try {
        const { error: funcError } = await supabase.rpc('get_unread_notification_count');
        status.functions = !funcError;
      } catch (e) {
        console.log('Functions check:', e);
      }

      setDbStatus(status);
      
      if (status.notifications && status.workspaces && status.tasks) {
        toast.success('Database schema is ready!');
      } else {
        toast.warning('Some database tables may be missing');
      }
    } catch (error) {
      console.error('Database check error:', error);
      toast.error('Failed to check database status');
    } finally {
      setIsChecking(false);
    }
  };

  const createNotificationsTable = async () => {
    try {
      // This would typically be done in Supabase SQL Editor
      toast.info('Please run NOTIFICATIONS_SCHEMA.sql in Supabase SQL Editor');
    } catch (error) {
      console.error('Error:', error);
      toast.error('Please run the schema manually in Supabase');
    }
  };

  useEffect(() => {
    if (user) {
      checkDatabase();
    }
  }, [user]);

  const getStatusIcon = (status: boolean) => {
    return status ? 
      <CheckCircle className="h-4 w-4 text-green-500" /> : 
      <XCircle className="h-4 w-4 text-red-500" />;
  };

  const getStatusBadge = (status: boolean) => {
    return status ? 
      <Badge variant="default" className="bg-green-500">Ready</Badge> : 
      <Badge variant="destructive">Missing</Badge>;
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Database Schema Status
        </CardTitle>
        <CardDescription>
          Verify that all required database tables and functions exist
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between">
          <span>Database Connection</span>
          <div className="flex items-center gap-2">
            {user ? (
              <>
                <CheckCircle className="h-4 w-4 text-green-500" />
                <Badge variant="default" className="bg-green-500">Connected</Badge>
              </>
            ) : (
              <>
                <XCircle className="h-4 w-4 text-red-500" />
                <Badge variant="destructive">Not Authenticated</Badge>
              </>
            )}
          </div>
        </div>

        {dbStatus && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span>Notifications Table</span>
              <div className="flex items-center gap-2">
                {getStatusIcon(dbStatus.notifications)}
                {getStatusBadge(dbStatus.notifications)}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span>Workspaces Table</span>
              <div className="flex items-center gap-2">
                {getStatusIcon(dbStatus.workspaces)}
                {getStatusBadge(dbStatus.workspaces)}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span>Tasks Table</span>
              <div className="flex items-center gap-2">
                {getStatusIcon(dbStatus.tasks)}
                {getStatusBadge(dbStatus.tasks)}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span>User Activity Table</span>
              <div className="flex items-center gap-2">
                {getStatusIcon(dbStatus.user_activity)}
                {getStatusBadge(dbStatus.user_activity)}
              </div>
            </div>

            <div className="flex items-center justify-between">
              <span>Notification Functions</span>
              <div className="flex items-center gap-2">
                {getStatusIcon(dbStatus.functions)}
                {getStatusBadge(dbStatus.functions)}
              </div>
            </div>
          </div>
        )}

        <div className="flex gap-2">
          <Button 
            onClick={checkDatabase}
            disabled={isChecking || !user}
            variant="outline"
          >
            {isChecking ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <Database className="h-4 w-4 mr-2" />
            )}
            Check Database
          </Button>

          {dbStatus && !dbStatus.notifications && (
            <Button 
              onClick={createNotificationsTable}
              variant="default"
            >
              Setup Notifications
            </Button>
          )}
        </div>

        {!user && (
          <div className="text-sm text-muted-foreground">
            Please sign in to check database status
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DatabaseTest;
