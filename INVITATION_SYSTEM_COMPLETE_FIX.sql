-- =====================================================
-- COMPLETE INVITATION SYSTEM FIX
-- =====================================================
-- This script fixes all invitation system issues:
-- 1. Creates/updates workspace_invitations table with correct columns
-- 2. Creates proper database functions
-- 3. Sets up RLS policies
-- 4. Fixes column name inconsistencies

-- STEP 1: Drop existing functions to recreate them properly
DROP FUNCTION IF EXISTS accept_workspace_invitation(UUID);
DROP FUNCTION IF EXISTS accept_workspace_invitation(UUID, UUID);
DROP FUNCTION IF EXISTS decline_workspace_invitation(UUID, UUID);

-- STEP 2: Create/update workspace_invitations table
CREATE TABLE IF NOT EXISTS workspace_invitations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  workspace_id UUID NOT NULL REFERENCES workspaces(id) ON DELETE CASCADE,
  workspace_name TEXT NOT NULL,
  invited_by UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  invited_by_name TEXT NOT NULL,
  invited_email TEXT NOT NULL,
  role TEXT NOT NULL CHECK (role IN ('member', 'admin', 'owner')),
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'accepted', 'declined', 'expired')),
  invite_code TEXT,
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days'),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- STEP 3: Add missing columns if they don't exist
DO $$ 
BEGIN
  -- Add invite_code column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'workspace_invitations' 
                 AND column_name = 'invite_code') THEN
    ALTER TABLE workspace_invitations ADD COLUMN invite_code TEXT;
  END IF;
  
  -- Add expires_at column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                 WHERE table_name = 'workspace_invitations' 
                 AND column_name = 'expires_at') THEN
    ALTER TABLE workspace_invitations ADD COLUMN expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '7 days');
  END IF;
END $$;

-- STEP 4: Create accept_workspace_invitation function (with user_id parameter)
CREATE OR REPLACE FUNCTION accept_workspace_invitation(
  invitation_id UUID,
  user_id UUID
) RETURNS BOOLEAN AS $$
DECLARE
  invitation_record workspace_invitations%ROWTYPE;
  user_email TEXT;
  existing_member_count INTEGER;
BEGIN
  -- Get user email
  SELECT email INTO user_email FROM auth.users WHERE id = user_id;
  
  IF user_email IS NULL THEN
    RAISE EXCEPTION 'User not found';
  END IF;

  -- Get the invitation
  SELECT * INTO invitation_record 
  FROM workspace_invitations 
  WHERE id = invitation_id 
  AND status = 'pending' 
  AND expires_at > NOW()
  AND invited_email = user_email;
  
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Invalid invitation or not authorized';
  END IF;

  -- Check if user is already a member
  SELECT COUNT(*) INTO existing_member_count
  FROM workspace_members 
  WHERE workspace_id = invitation_record.workspace_id 
  AND user_id = user_id;
  
  IF existing_member_count > 0 THEN
    RAISE EXCEPTION 'User is already a member of this workspace';
  END IF;

  -- Add user as workspace member
  INSERT INTO workspace_members (
    workspace_id,
    user_id,
    role,
    joined_at,
    invited_by
  ) VALUES (
    invitation_record.workspace_id,
    user_id,
    invitation_record.role,
    NOW(),
    invitation_record.invited_by
  );
  
  -- Update invitation status
  UPDATE workspace_invitations 
  SET status = 'accepted', updated_at = NOW()
  WHERE id = invitation_id;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- STEP 5: Create decline_workspace_invitation function
CREATE OR REPLACE FUNCTION decline_workspace_invitation(
  invitation_id UUID,
  user_id UUID
) RETURNS VOID AS $$
DECLARE
  invitation_record workspace_invitations%ROWTYPE;
  user_email TEXT;
BEGIN
  -- Get user email
  SELECT email INTO user_email FROM auth.users WHERE id = user_id;
  
  IF user_email IS NULL THEN
    RAISE EXCEPTION 'User not found';
  END IF;

  -- Get the invitation details
  SELECT * INTO invitation_record
  FROM workspace_invitations
  WHERE id = invitation_id
    AND status = 'pending'
    AND invited_email = user_email;

  -- Check if invitation exists and belongs to user
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Invalid invitation or not authorized';
  END IF;

  -- Update invitation status
  UPDATE workspace_invitations
  SET status = 'declined', updated_at = NOW()
  WHERE id = invitation_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- STEP 6: Create function to clean up expired invitations
CREATE OR REPLACE FUNCTION cleanup_expired_invitations()
RETURNS void AS $$
BEGIN
  UPDATE workspace_invitations 
  SET status = 'expired' 
  WHERE status = 'pending' 
  AND expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- STEP 7: Grant necessary permissions
GRANT EXECUTE ON FUNCTION accept_workspace_invitation(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION decline_workspace_invitation(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION cleanup_expired_invitations() TO authenticated;

-- STEP 8: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_workspace_invitations_invited_email 
ON workspace_invitations(invited_email);

CREATE INDEX IF NOT EXISTS idx_workspace_invitations_invite_code 
ON workspace_invitations(invite_code);

CREATE INDEX IF NOT EXISTS idx_workspace_invitations_status_expires 
ON workspace_invitations(status, expires_at);

-- STEP 9: Set up RLS policies
ALTER TABLE workspace_invitations ENABLE ROW LEVEL SECURITY;

-- Policy for users to see invitations sent to their email
CREATE POLICY "Users can view their own invitations" ON workspace_invitations
  FOR SELECT USING (
    invited_email = (SELECT email FROM auth.users WHERE id = auth.uid())
  );

-- Policy for workspace owners/admins to view invitations they sent
CREATE POLICY "Workspace admins can view sent invitations" ON workspace_invitations
  FOR SELECT USING (
    invited_by = auth.uid() OR
    EXISTS (
      SELECT 1 FROM workspace_members 
      WHERE workspace_id = workspace_invitations.workspace_id 
      AND user_id = auth.uid() 
      AND role IN ('owner', 'admin')
    )
  );

-- Policy for creating invitations
CREATE POLICY "Workspace admins can create invitations" ON workspace_invitations
  FOR INSERT WITH CHECK (
    invited_by = auth.uid() AND
    EXISTS (
      SELECT 1 FROM workspace_members 
      WHERE workspace_id = workspace_invitations.workspace_id 
      AND user_id = auth.uid() 
      AND role IN ('owner', 'admin')
    )
  );

-- Policy for updating invitations (for status changes)
CREATE POLICY "Users can update their invitations" ON workspace_invitations
  FOR UPDATE USING (
    invited_email = (SELECT email FROM auth.users WHERE id = auth.uid()) OR
    invited_by = auth.uid()
  );

-- STEP 10: Update any existing invitations that might be missing invite codes
UPDATE workspace_invitations 
SET invite_code = CONCAT(
  substring(md5(random()::text) from 1 for 8),
  substring(md5(random()::text) from 1 for 8)
)
WHERE invite_code = '' OR invite_code IS NULL;

-- STEP 11: Clean up any expired invitations
SELECT cleanup_expired_invitations();

-- STEP 12: Success message
DO $$
BEGIN
  RAISE NOTICE 'Invitation system fix completed successfully!';
  RAISE NOTICE 'Functions created: accept_workspace_invitation, decline_workspace_invitation, cleanup_expired_invitations';
  RAISE NOTICE 'RLS policies created for security';
  RAISE NOTICE 'Indexes created for better performance';
  RAISE NOTICE 'Expired invitations cleaned up';
END $$;

-- STEP 13: Verification queries
SELECT 'workspace_invitations table structure:' as info;
SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'workspace_invitations'
AND table_schema = 'public'
ORDER BY ordinal_position;

SELECT 'Available functions:' as info;
SELECT 
  routine_name,
  routine_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name LIKE '%invitation%'
ORDER BY routine_name;
