-- =====================================================
-- INVITATION SYSTEM FIX
-- Run this SQL to fix the invitation system
-- =====================================================

-- 1. Ensure the workspace_invitations table has the correct structure
-- (This should already exist from the schema, but let's make sure)

-- Check if invite_code column exists and add if missing
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_name = 'workspace_invitations' 
        AND column_name = 'invite_code'
    ) THEN
        ALTER TABLE workspace_invitations ADD COLUMN invite_code TEXT NOT NULL DEFAULT '';
    END IF;
END $$;

-- 2. Create the accept_workspace_invitation function
CREATE OR REPLACE FUNCTION accept_workspace_invitation(
  invitation_id UUID,
  user_id UUID
) RETURNS VOID AS $$
DECLARE
  invitation_record workspace_invitations%ROWTYPE;
BEGIN
  -- Get the invitation details
  SELECT * INTO invitation_record
  FROM workspace_invitations
  WHERE id = invitation_id
    AND status = 'pending'
    AND expires_at > NOW();

  -- Check if invitation exists and is valid
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Invalid or expired invitation';
  END IF;

  -- Check if user is already a member
  IF EXISTS (
    SELECT 1 FROM workspace_members
    WHERE workspace_id = invitation_record.workspace_id
      AND user_id = user_id
  ) THEN
    RAISE EXCEPTION 'User is already a member of this workspace';
  END IF;

  -- Add user to workspace
  INSERT INTO workspace_members (
    workspace_id,
    user_id,
    role,
    joined_at
  ) VALUES (
    invitation_record.workspace_id,
    user_id,
    invitation_record.role,
    NOW()
  );

  -- Update invitation status
  UPDATE workspace_invitations
  SET status = 'accepted'
  WHERE id = invitation_id;

  -- Log activity
  INSERT INTO user_activities (
    user_id,
    workspace_id,
    activity_type,
    activity_data,
    timestamp
  ) VALUES (
    user_id,
    invitation_record.workspace_id,
    'member_joined',
    jsonb_build_object(
      'workspace_name', invitation_record.workspace_name,
      'invited_by', invitation_record.invited_by_name
    ),
    NOW()
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 3. Create the decline_workspace_invitation function
CREATE OR REPLACE FUNCTION decline_workspace_invitation(
  invitation_id UUID,
  user_id UUID
) RETURNS VOID AS $$
DECLARE
  invitation_record workspace_invitations%ROWTYPE;
BEGIN
  -- Get the invitation details
  SELECT * INTO invitation_record
  FROM workspace_invitations
  WHERE id = invitation_id
    AND status = 'pending'
    AND invitee_email = (
      SELECT email FROM auth.users WHERE id = user_id
    );

  -- Check if invitation exists and belongs to user
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Invalid invitation or not authorized';
  END IF;

  -- Update invitation status
  UPDATE workspace_invitations
  SET status = 'declined'
  WHERE id = invitation_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 4. Create function to clean up expired invitations
CREATE OR REPLACE FUNCTION cleanup_expired_invitations() RETURNS VOID AS $$
BEGIN
  UPDATE workspace_invitations
  SET status = 'expired'
  WHERE status = 'pending'
    AND expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- 5. Create a function to get workspace invitation by code
CREATE OR REPLACE FUNCTION get_invitation_by_code(
  invite_code_param TEXT
) RETURNS TABLE (
  id UUID,
  workspace_id UUID,
  workspace_name TEXT,
  invited_by_name TEXT,
  role user_role,
  expires_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    wi.id,
    wi.workspace_id,
    wi.workspace_name,
    wi.invited_by_name,
    wi.role,
    wi.expires_at
  FROM workspace_invitations wi
  WHERE wi.invite_code = invite_code_param
    AND wi.status = 'pending'
    AND wi.expires_at > NOW();
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 6. Grant necessary permissions
GRANT EXECUTE ON FUNCTION accept_workspace_invitation(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION decline_workspace_invitation(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_invitation_by_code(TEXT) TO authenticated;

-- 7. Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_workspace_invitations_invitee_email 
ON workspace_invitations(invitee_email);

CREATE INDEX IF NOT EXISTS idx_workspace_invitations_invite_code 
ON workspace_invitations(invite_code);

CREATE INDEX IF NOT EXISTS idx_workspace_invitations_status_expires 
ON workspace_invitations(status, expires_at);

-- 8. Update any existing invitations that might be missing invite codes
UPDATE workspace_invitations 
SET invite_code = CONCAT(
  substring(md5(random()::text) from 1 for 8),
  substring(md5(random()::text) from 1 for 8)
)
WHERE invite_code = '' OR invite_code IS NULL;

-- 9. Clean up any expired invitations
SELECT cleanup_expired_invitations();

-- Success message
DO $$
BEGIN
  RAISE NOTICE 'Invitation system fix completed successfully!';
  RAISE NOTICE 'Functions created: accept_workspace_invitation, decline_workspace_invitation, get_invitation_by_code';
  RAISE NOTICE 'Indexes created for better performance';
  RAISE NOTICE 'Expired invitations cleaned up';
END $$;
