
SELECT 'Checking notifications table...' as step;

-- Check if notifications table exists
SELECT 
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM information_schema.tables 
      WHERE table_name = 'notifications' AND table_schema = 'public'
    ) 
    THEN '✅ notifications table exists'
    ELSE '❌ notifications table missing - run NOTIFICATIONS_SCHEMA.sql'
  END as notifications_table_status;

-- Check table structure
SELECT 'Checking table structure...' as step;
SELECT 
  column_name,
  data_type,
  is_nullable,
  column_default
FROM information_schema.columns 
WHERE table_name = 'notifications' AND table_schema = 'public'
ORDER BY ordinal_position;

-- =====================================================
-- 2. CHECK RLS POLICIES
-- =====================================================
SELECT 'Checking RLS policies...' as step;

-- Check if RLS is enabled
SELECT 
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM pg_tables 
      WHERE tablename = 'notifications' AND rowsecurity = true
    )
    THEN '✅ RLS enabled on notifications table'
    ELSE '❌ RLS not enabled - run NOTIFICATIONS_SCHEMA.sql'
  END as rls_status;

-- List all policies
SELECT 
  policyname,
  permissive,
  roles,
  cmd,
  qual
FROM pg_policies 
WHERE tablename = 'notifications';

-- =====================================================
-- 3. CHECK FUNCTIONS
-- =====================================================
SELECT 'Checking notification functions...' as step;

-- Check if notification functions exist
SELECT 
  routine_name,
  routine_type,
  'exists' as status
FROM information_schema.routines 
WHERE routine_schema = 'public' 
  AND routine_name IN (
    'create_notification',
    'mark_notification_read',
    'mark_all_notifications_read',
    'get_unread_notification_count'
  )
ORDER BY routine_name;

-- =====================================================
-- 4. CHECK WORKSPACE TABLES
-- =====================================================
SELECT 'Checking workspace tables...' as step;

-- Check if required tables exist
SELECT 
  table_name,
  CASE 
    WHEN table_name = 'workspaces' THEN '✅ workspaces table exists'
    WHEN table_name = 'workspace_members' THEN '✅ workspace_members table exists'
    WHEN table_name = 'workspace_invitations' THEN '✅ workspace_invitations table exists'
    WHEN table_name = 'tasks' THEN '✅ tasks table exists'
    WHEN table_name = 'user_activity' THEN '✅ user_activity table exists'
    ELSE '✅ table exists'
  END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_name IN (
    'workspaces', 
    'workspace_members', 
    'workspace_invitations',
    'tasks',
    'user_activity'
  )
ORDER BY table_name;

-- =====================================================
-- 5. CHECK REALTIME SUBSCRIPTIONS
-- =====================================================
SELECT 'Checking realtime setup...' as step;

-- Check if notifications table is in realtime publication
SELECT 
  CASE 
    WHEN EXISTS (
      SELECT 1 FROM pg_publication_tables 
      WHERE pubname = 'supabase_realtime' 
        AND tablename = 'notifications'
    )
    THEN '✅ notifications table enabled for realtime'
    ELSE '⚠️ notifications table not in realtime - may need manual setup'
  END as realtime_status;

-- =====================================================
-- 6. TEST BASIC FUNCTIONALITY
-- =====================================================
SELECT 'Testing basic functionality...' as step;

-- Test notification creation (only if user is authenticated)
DO $$
BEGIN
  IF auth.uid() IS NOT NULL THEN
    -- Try to create a test notification
    INSERT INTO notifications (
      user_id, 
      type, 
      title, 
      message, 
      data
    ) VALUES (
      auth.uid(),
      'system',
      'Email System Test',
      'This is a test notification to verify the email system setup.',
      '{"test": true}'::jsonb
    );
    
    RAISE NOTICE '✅ Test notification created successfully';
  ELSE
    RAISE NOTICE '⚠️ No authenticated user - skipping notification test';
  END IF;
EXCEPTION WHEN OTHERS THEN
  RAISE NOTICE '❌ Failed to create test notification: %', SQLERRM;
END $$;

-- Check notification count
SELECT 
  COUNT(*) as total_notifications,
  COUNT(*) FILTER (WHERE read = false) as unread_notifications,
  COUNT(*) FILTER (WHERE type = 'system') as system_notifications
FROM notifications 
WHERE user_id = auth.uid();

-- =====================================================
-- 7. ENVIRONMENT CHECK
-- =====================================================
SELECT 'Environment configuration check...' as step;

-- Note: These environment variables are checked in the application, not database
SELECT '
📧 EMAIL CONFIGURATION CHECKLIST:

1. Environment Variables (.env.local):
   ✓ VITE_EMAIL_SERVICE_PROVIDER=resend
   ✓ VITE_EMAIL_SERVICE_API_KEY=re_your_api_key_here
   ✓ VITE_FROM_EMAIL=<EMAIL>
   ✓ VITE_FROM_NAME=EasTask Team
   ✓ VITE_APP_URL=http://localhost:8081

2. Resend Setup:
   ✓ Account created at resend.com
   ✓ API key generated
   ✓ Domain verified (for production)

3. Testing:
   ✓ Navigate to /email-test
   ✓ Test connection
   ✓ Send test emails
   ✓ Verify email delivery

' as configuration_notes;

-- =====================================================
-- 8. CLEANUP TEST DATA
-- =====================================================
-- Remove test notification if created
DELETE FROM notifications 
WHERE user_id = auth.uid() 
  AND type = 'system' 
  AND title = 'Email System Test';

-- =====================================================
-- 9. FINAL STATUS
-- =====================================================
SELECT 'Final verification...' as step;

-- Overall system status
SELECT 
  CASE 
    WHEN (
      -- Check all required components
      EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notifications') AND
      EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'notifications' AND rowsecurity = true) AND
      EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'create_notification') AND
      EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'workspaces') AND
      EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'tasks')
    )
    THEN '🎉 EMAIL SYSTEM SETUP COMPLETE! 
    
✅ Database schema ready
✅ Notifications table configured
✅ RLS policies enabled
✅ Functions created
✅ Required tables exist

🚀 NEXT STEPS:
1. Configure environment variables
2. Test email service at /email-test
3. Send test invitations and assignments
4. Verify real-time notifications work

Ready for production! 📧'
    ELSE '❌ EMAIL SYSTEM SETUP INCOMPLETE

Please run NOTIFICATIONS_SCHEMA.sql first, then re-run this verification.'
  END as final_status;

-- Success message
SELECT '✅ Email system verification complete!' as result;
