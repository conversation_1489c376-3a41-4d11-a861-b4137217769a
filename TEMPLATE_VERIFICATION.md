# 🔧 SUPABASE EMAIL TEMPLATE VERIFICATION

## 🚨 **CURRENT ISSUE**
Your debug shows `refresh_token: ""` (empty string), which means the Supabase email template is still not configured correctly.

## ✅ **EXACT TEMPLATE TO USE**

Copy and paste this EXACT template into your Supabase Password Recovery email template:

```html
<h2>Reset Your EasTask Password</h2>

<p>Hi there,</p>

<p>We received a request to reset your password for your EasTask account. Click the button below to create a new password:</p>

<p><a href="{{ .SiteURL }}/reset-password?access_token={{ .TokenHash }}&refresh_token={{ .RefreshTokenHash }}&type=recovery">Reset Password</a></p>

<p>This link will expire in {{ .ExpiresIn }} for security reasons.</p>

<p>If you didn't request a password reset, you can safely ignore this email.</p>

<p>Best regards,<br>
The EasTask Team</p>
```

## 🔍 **CRITICAL VERIFICATION POINTS**

### **1. Check the URL Line:**
The most important line is:
```html
<a href="{{ .SiteURL }}/reset-password?access_token={{ .TokenHash }}&refresh_token={{ .RefreshTokenHash }}&type=recovery">Reset Password</a>
```

### **2. Verify These Parameters:**
- `{{ .SiteURL }}` ✅
- `{{ .TokenHash }}` ✅ (for access_token)
- `{{ .RefreshTokenHash }}` ⚠️ **THIS IS WHAT'S MISSING**
- `type=recovery` ✅

## 📋 **STEP-BY-STEP CHECKLIST**

### **Step 1: Access Supabase**
- [ ] Go to https://app.supabase.com
- [ ] Select your EasTask project
- [ ] Navigate to Authentication → Email Templates

### **Step 2: Edit Password Recovery Template**
- [ ] Click on "Password Recovery" template
- [ ] **DELETE** all existing content
- [ ] **PASTE** the exact template above
- [ ] **VERIFY** the URL contains `{{ .RefreshTokenHash }}`
- [ ] **SAVE** the template

### **Step 3: Verify Settings**
- [ ] Go to Authentication → Settings → General
- [ ] Site URL: `http://localhost:8081`
- [ ] Additional Redirect URLs: `http://localhost:8081/reset-password`

### **Step 4: Test with Fresh Request**
- [ ] Go to your app: http://localhost:8081
- [ ] Click "Forgot Password?"
- [ ] Enter your email
- [ ] Check your email for the NEW reset link
- [ ] Click the reset link

## 🔍 **WHAT TO LOOK FOR IN THE EMAIL**

The reset link in your email should look like:
```
http://localhost:8081/reset-password?access_token=LONG_TOKEN&refresh_token=ANOTHER_LONG_TOKEN&type=recovery
```

### **BEFORE FIX (BROKEN):**
```
❌ http://localhost:8081/reset-password?access_token=TOKEN&type=recovery
```

### **AFTER FIX (WORKING):**
```
✅ http://localhost:8081/reset-password?access_token=TOKEN&refresh_token=TOKEN&type=recovery
```

## 🚨 **COMMON MISTAKES TO AVOID**

1. **Don't use old reset emails** - they won't have the new parameters
2. **Don't forget the curly braces** - `{{ .RefreshTokenHash }}` not `.RefreshTokenHash`
3. **Don't miss the ampersand** - `&refresh_token=` not `refresh_token=`
4. **Don't forget to save** the template in Supabase

## 🔧 **TROUBLESHOOTING**

### **If refresh_token is still empty:**
1. **Clear browser cache**
2. **Request a completely NEW password reset**
3. **Check Supabase logs** for any errors
4. **Verify the template was actually saved**

### **If the email doesn't arrive:**
1. **Check spam folder**
2. **Verify email settings** in Supabase
3. **Try a different email address**

## 🎯 **SUCCESS INDICATORS**

After fixing the template, your debug component should show:
- ✅ access_token ✓ Present
- ✅ refresh_token ✓ Present  ← **This should change from Missing to Present**
- ✅ type: recovery ✓

---

**The key is making sure `{{ .RefreshTokenHash }}` is included in the email template URL!**
