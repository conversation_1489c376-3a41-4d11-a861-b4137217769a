import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { emailService } from '../services/emailService';
import { notificationService } from '../services/notificationService';
import { useSupabaseAuth } from '../contexts/SupabaseAuthContext';
import { useSupabaseWorkspace } from '../contexts/SupabaseWorkspaceContext';
import { 
  Mail, 
  Send, 
  CheckCircle, 
  XCircle, 
  Loader2, 
  TestTube,
  Users,
  MessageSquare,
  Bell,
  Settings
} from 'lucide-react';
import { toast } from 'sonner';

const EmailServiceTest: React.FC = () => {
  const { user } = useSupabaseAuth();
  const { currentWorkspace } = useSupabaseWorkspace();
  
  const [connectionStatus, setConnectionStatus] = useState<{
    success: boolean;
    message: string;
    provider: string;
  } | null>(null);
  
  const [testEmail, setTestEmail] = useState('');
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [isSendingInvite, setIsSendingInvite] = useState(false);
  const [isSendingTaskAssignment, setIsSendingTaskAssignment] = useState(false);
  
  const [inviteData, setInviteData] = useState({
    email: '',
    role: 'member'
  });
  
  const [taskData, setTaskData] = useState({
    email: '',
    title: 'Sample Task Assignment',
    description: 'This is a test task assignment to verify email functionality.',
    priority: 'medium',
    dueDate: ''
  });

  // Test email service connection
  const testConnection = async () => {
    setIsTestingConnection(true);
    try {
      const result = await emailService.testConnection();
      setConnectionStatus(result);
      
      if (result.success) {
        toast.success('Email service connection successful!');
      } else {
        toast.error('Email service connection failed');
      }
    } catch (error) {
      console.error('Connection test error:', error);
      setConnectionStatus({
        success: false,
        message: 'Connection test failed',
        provider: 'unknown'
      });
      toast.error('Connection test failed');
    } finally {
      setIsTestingConnection(false);
    }
  };

  // Send test workspace invitation
  const sendTestInvitation = async () => {
    if (!inviteData.email || !currentWorkspace || !user) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsSendingInvite(true);
    try {
      const success = await notificationService.sendWorkspaceInvitation({
        invitedEmail: inviteData.email,
        inviterUserId: user.id,
        workspaceId: currentWorkspace.id,
        workspaceName: currentWorkspace.name,
        inviterName: user.user_metadata?.full_name || user.email || 'Unknown User',
        inviterEmail: user.email,
        role: inviteData.role,
        inviteCode: currentWorkspace.invite_code || 'test-code'
      });

      if (success) {
        toast.success(`Invitation email sent to ${inviteData.email}!`);
        setInviteData({ email: '', role: 'member' });
      } else {
        toast.error('Failed to send invitation email');
      }
    } catch (error) {
      console.error('Invitation send error:', error);
      toast.error('Failed to send invitation');
    } finally {
      setIsSendingInvite(false);
    }
  };

  // Send test task assignment
  const sendTestTaskAssignment = async () => {
    if (!taskData.email || !currentWorkspace || !user) {
      toast.error('Please fill in all required fields');
      return;
    }

    setIsSendingTaskAssignment(true);
    try {
      await notificationService.sendTaskAssignment({
        taskId: 'test-task-' + Date.now(),
        taskTitle: taskData.title,
        taskDescription: taskData.description,
        assigneeUserId: 'test-user-id',
        assigneeEmail: taskData.email,
        assignerUserId: user.id,
        assignerName: user.user_metadata?.full_name || user.email || 'Unknown User',
        workspaceId: currentWorkspace.id,
        workspaceName: currentWorkspace.name,
        pageTitle: 'Test Page',
        dueDate: taskData.dueDate,
        priority: taskData.priority
      });

      toast.success(`Task assignment email sent to ${taskData.email}!`);
      setTaskData({
        email: '',
        title: 'Sample Task Assignment',
        description: 'This is a test task assignment to verify email functionality.',
        priority: 'medium',
        dueDate: ''
      });
    } catch (error) {
      console.error('Task assignment send error:', error);
      toast.error('Failed to send task assignment');
    } finally {
      setIsSendingTaskAssignment(false);
    }
  };

  // Auto-test connection on component mount
  useEffect(() => {
    testConnection();
  }, []);

  const getStatusIcon = () => {
    if (!connectionStatus) return <Loader2 className="h-4 w-4 animate-spin" />;
    return connectionStatus.success ? 
      <CheckCircle className="h-4 w-4 text-green-500" /> : 
      <XCircle className="h-4 w-4 text-red-500" />;
  };

  const getStatusBadge = () => {
    if (!connectionStatus) return <Badge variant="secondary">Testing...</Badge>;
    return connectionStatus.success ? 
      <Badge variant="default" className="bg-green-500">Connected</Badge> : 
      <Badge variant="destructive">Failed</Badge>;
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Email Service Configuration
          </CardTitle>
          <CardDescription>
            Test and configure the email service for EasTask notifications
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-4 border rounded-lg">
            <div className="flex items-center gap-3">
              {getStatusIcon()}
              <div>
                <div className="font-medium">Email Service Status</div>
                <div className="text-sm text-muted-foreground">
                  Provider: {connectionStatus?.provider || 'Unknown'}
                </div>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {getStatusBadge()}
              <Button 
                variant="outline" 
                size="sm" 
                onClick={testConnection}
                disabled={isTestingConnection}
              >
                {isTestingConnection ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <TestTube className="h-4 w-4 mr-2" />
                )}
                Test Connection
              </Button>
            </div>
          </div>

          {connectionStatus && (
            <Alert>
              <AlertDescription>
                {connectionStatus.message}
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Workspace Invitation Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Test Workspace Invitation
            </CardTitle>
            <CardDescription>
              Send a test workspace invitation email
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="invite-email">Email Address</Label>
              <Input
                id="invite-email"
                type="email"
                placeholder="<EMAIL>"
                value={inviteData.email}
                onChange={(e) => setInviteData({ ...inviteData, email: e.target.value })}
              />
            </div>
            
            <div>
              <Label htmlFor="invite-role">Role</Label>
              <select
                id="invite-role"
                className="w-full p-2 border rounded-md"
                value={inviteData.role}
                onChange={(e) => setInviteData({ ...inviteData, role: e.target.value })}
              >
                <option value="member">Member</option>
                <option value="admin">Admin</option>
                <option value="viewer">Viewer</option>
              </select>
            </div>

            <Button 
              onClick={sendTestInvitation}
              disabled={isSendingInvite || !inviteData.email}
              className="w-full"
            >
              {isSendingInvite ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Send className="h-4 w-4 mr-2" />
              )}
              Send Test Invitation
            </Button>
          </CardContent>
        </Card>

        {/* Task Assignment Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Test Task Assignment
            </CardTitle>
            <CardDescription>
              Send a test task assignment email
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="task-email">Email Address</Label>
              <Input
                id="task-email"
                type="email"
                placeholder="<EMAIL>"
                value={taskData.email}
                onChange={(e) => setTaskData({ ...taskData, email: e.target.value })}
              />
            </div>
            
            <div>
              <Label htmlFor="task-title">Task Title</Label>
              <Input
                id="task-title"
                placeholder="Task title"
                value={taskData.title}
                onChange={(e) => setTaskData({ ...taskData, title: e.target.value })}
              />
            </div>

            <div>
              <Label htmlFor="task-description">Description</Label>
              <Textarea
                id="task-description"
                placeholder="Task description"
                value={taskData.description}
                onChange={(e) => setTaskData({ ...taskData, description: e.target.value })}
                rows={3}
              />
            </div>

            <Button 
              onClick={sendTestTaskAssignment}
              disabled={isSendingTaskAssignment || !taskData.email}
              className="w-full"
            >
              {isSendingTaskAssignment ? (
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
              ) : (
                <Send className="h-4 w-4 mr-2" />
              )}
              Send Test Assignment
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Configuration Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Current Configuration
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <div className="font-medium">Provider</div>
              <div className="text-muted-foreground">{import.meta.env.VITE_EMAIL_SERVICE_PROVIDER || 'demo'}</div>
            </div>
            <div>
              <div className="font-medium">From Email</div>
              <div className="text-muted-foreground">{import.meta.env.VITE_FROM_EMAIL || '<EMAIL>'}</div>
            </div>
            <div>
              <div className="font-medium">From Name</div>
              <div className="text-muted-foreground">{import.meta.env.VITE_FROM_NAME || 'EasTask Team'}</div>
            </div>
            <div>
              <div className="font-medium">API Key</div>
              <div className="text-muted-foreground">
                {import.meta.env.VITE_EMAIL_SERVICE_API_KEY ? '✅ Configured' : '❌ Not Set'}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EmailServiceTest;
