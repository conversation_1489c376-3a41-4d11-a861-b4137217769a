import React, { useState, useEffect } from 'react';
import { useTask } from '../contexts/TaskContext';
import { useAuth } from '../contexts/SupabaseAuthContext';
import { handleDragOver, handleDragEnter, handleDragLeave, handleDrop } from '../utils/dragDrop';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Plus, 
  Inbox, 
  BarChart3, 
  CheckCircle2, 
  Globe, 
  Timer, 
  MessageCircle, 
  GitBranch, 
  Layers,
  Settings,
  PlayCircle,
  PauseCircle,
  Clock
} from 'lucide-react';
import TaskCard from '../components/TaskCard';
import PageCard from '../components/PageCard';
import AddTaskModal from '../components/AddTaskModal';
import AddPageModal from '../components/AddPageModal';
import ModernLayout from '../components/ModernLayout';
import { Link } from 'react-router-dom';
import { supabase } from '../lib/supabase';

const Tasker: React.FC = () => {
  const { state, moveTask, searchTasks, updateTask } = useTask();
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [showAddPageModal, setShowAddPageModal] = useState(false);
  const [showAddTaskModal, setShowAddTaskModal] = useState(false);
  const [selectedTask, setSelectedTask] = useState<any>(null);
  const [showTaskDetails, setShowTaskDetails] = useState(false);
  const [advancedStats, setAdvancedStats] = useState({
    totalTimeTracked: 0,
    activeTimers: 0,
    totalComments: 0,
    totalDependencies: 0,
    totalSubtasks: 0,
    databaseSetup: false
  });

  // Load advanced features stats
  useEffect(() => {
    loadAdvancedStats();
  }, [user]);

  const loadAdvancedStats = async () => {
    if (!user) return;

    try {
      // Check if advanced features tables exist
      const { data: tables } = await supabase
        .from('information_schema.tables')
        .select('table_name')
        .eq('table_schema', 'public')
        .in('table_name', ['task_dependencies', 'subtasks', 'task_comments', 'task_time_entries']);

      const databaseSetup = tables?.length === 4;

      if (!databaseSetup) {
        setAdvancedStats(prev => ({ ...prev, databaseSetup: false }));
        return;
      }

      // Load stats
      const [timeEntries, comments, dependencies, subtasks] = await Promise.all([
        supabase.from('task_time_entries').select('duration_minutes, end_time').eq('user_id', user.id),
        supabase.from('task_comments').select('*', { count: 'exact', head: true }).eq('user_id', user.id),
        supabase.from('task_dependencies').select('*', { count: 'exact', head: true }).eq('created_by', user.id),
        supabase.from('subtasks').select('*').eq('created_by', user.id)
      ]);

      const totalTimeTracked = timeEntries.data?.reduce((sum, entry) => sum + (entry.duration_minutes || 0), 0) || 0;
      const activeTimers = timeEntries.data?.filter(entry => !entry.end_time).length || 0;

      setAdvancedStats({
        totalTimeTracked,
        activeTimers,
        totalComments: comments.count || 0,
        totalDependencies: dependencies.count || 0,
        totalSubtasks: subtasks.data?.length || 0,
        databaseSetup: true
      });

    } catch (error) {
      console.error('Error loading advanced stats:', error);
    }
  };

  const openTaskDetails = (task: any) => {
    setSelectedTask(task);
    setShowTaskDetails(true);
  };

  if (state.pages.length === 0) {
    return (
      <ModernLayout>
        <div className="max-w-4xl mx-auto">
          <Card className="card-modern p-8 sm:p-12 text-center">
            <div className="max-w-md mx-auto space-y-6">
              <div className="w-20 h-20 bg-gradient-to-r from-orange-500 to-orange-600 rounded-2xl mx-auto flex items-center justify-center">
                <Globe className="w-10 h-10 text-white" />
              </div>
              <div className="space-y-3">
                <h1 className="text-2xl sm:text-3xl font-bold text-foreground">Welcome to EasTask</h1>
                <p className="text-muted-foreground text-base sm:text-lg">
                  Create your first project to start organizing your tasks and boost your productivity.
                </p>
              </div>
              <div className="space-y-3">
                <Link to="/websites">
                  <Button size="lg" className="w-full sm:w-auto">
                    <Plus className="w-4 h-4 mr-2" />
                    Create Your First Project
                  </Button>
                </Link>
                <p className="text-xs text-muted-foreground">
                  Projects help you organize related tasks and collaborate with your team
                </p>
              </div>
            </div>
          </Card>
        </div>
      </ModernLayout>
    );
  }

  // Calculate stats
  const totalTasks = state.unassignedTasks.length + state.pages.reduce((sum, page) => sum + page.tasks.length, 0);
  const completedTasks = state.unassignedTasks.filter(task => task.status === 'done').length + 
                        state.pages.reduce((sum, page) => sum + page.tasks.filter(task => task.status === 'done').length, 0);
  const progressPercentage = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

  // Filter tasks based on search
  const filteredTasks = searchQuery 
    ? searchTasks(searchQuery)
    : state.unassignedTasks;

  const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;

  return (
    <ModernLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-4">
          <div className="space-y-2">
            <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-foreground">
              Task Manager
            </h1>
            <p className="text-muted-foreground text-sm sm:text-base">
              Organize your work and collaborate with your team
            </p>
          </div>
          <div className="flex gap-2">
            <Link to="/websites">
              <Button variant="outline" size="sm">
                <Globe className="w-4 h-4 mr-2" />
                Manage Projects
              </Button>
            </Link>
            <Button 
              onClick={() => setShowAddTaskModal(true)}
              size="sm"
            >
              <Plus className="w-4 h-4 mr-2" />
              Add Task
            </Button>
          </div>
        </div>

        {/* Stats Cards */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-3 sm:gap-4">
          <Card className="card-modern p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
                <BarChart3 className="w-4 h-4 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <p className="text-xs sm:text-sm text-muted-foreground">Total Tasks</p>
                <p className="text-lg sm:text-xl font-bold text-foreground">{totalTasks}</p>
              </div>
            </div>
          </Card>

          <Card className="card-modern p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 dark:bg-green-900 rounded-lg">
                <CheckCircle2 className="w-4 h-4 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <p className="text-xs sm:text-sm text-muted-foreground">Completed</p>
                <p className="text-lg sm:text-xl font-bold text-green-600 dark:text-green-400">{completedTasks}</p>
              </div>
            </div>
          </Card>

          <Card className="card-modern p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
                <Timer className="w-4 h-4 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <p className="text-xs sm:text-sm text-muted-foreground">Time Tracked</p>
                <p className="text-lg sm:text-xl font-bold text-orange-600 dark:text-orange-400">
                  {Math.floor(advancedStats.totalTimeTracked / 60)}h
                </p>
              </div>
            </div>
          </Card>

          <Card className="card-modern p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 dark:bg-purple-900 rounded-lg">
                <MessageCircle className="w-4 h-4 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <p className="text-xs sm:text-sm text-muted-foreground">Comments</p>
                <p className="text-lg sm:text-xl font-bold text-purple-600 dark:text-purple-400">{advancedStats.totalComments}</p>
              </div>
            </div>
          </Card>

          <Card className="card-modern p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-indigo-100 dark:bg-indigo-900 rounded-lg">
                <GitBranch className="w-4 h-4 text-indigo-600 dark:text-indigo-400" />
              </div>
              <div>
                <p className="text-xs sm:text-sm text-muted-foreground">Dependencies</p>
                <p className="text-lg sm:text-xl font-bold text-indigo-600 dark:text-indigo-400">{advancedStats.totalDependencies}</p>
              </div>
            </div>
          </Card>

          <Card className="card-modern p-4">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-teal-100 dark:bg-teal-900 rounded-lg">
                <Layers className="w-4 h-4 text-teal-600 dark:text-teal-400" />
              </div>
              <div>
                <p className="text-xs sm:text-sm text-muted-foreground">Subtasks</p>
                <p className="text-lg sm:text-xl font-bold text-teal-600 dark:text-teal-400">{advancedStats.totalSubtasks}</p>
              </div>
            </div>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid lg:grid-cols-4 gap-4 lg:gap-6">
          {/* Sidebar - Unassigned Tasks */}
          <div className="lg:col-span-1 order-2 lg:order-1">
            <Card className="card-modern sticky top-4">
              <CardHeader className="pb-3">
                <div className="flex items-center gap-2 mb-3">
                  <div className="p-2 bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg">
                    <Inbox className="w-4 h-4 text-white" />
                  </div>
                  <div>
                    <h2 className="font-semibold text-base sm:text-lg text-foreground">Tasks to Assign</h2>
                    <p className="text-xs text-muted-foreground">{state.unassignedTasks.length} unassigned</p>
                  </div>
                </div>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Search tasks..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 h-9"
                  />
                </div>
              </CardHeader>
              <CardContent
                className="space-y-2 max-h-96 overflow-y-auto"
                onDragOver={handleDragOver}
                onDragEnter={handleDragEnter}
                onDragLeave={handleDragLeave}
                onDrop={(e) => {
                  const dragData = handleDrop(e);
                  if (dragData && dragData.type === 'task') {
                    moveTask(dragData.taskId); // Move to unassigned
                  }
                }}
              >
                {filteredTasks.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    {searchQuery ? (
                      <div className="space-y-2">
                        <Search className="w-8 h-8 mx-auto opacity-50" />
                        <p className="text-sm">No tasks found</p>
                      </div>
                    ) : (
                      <div className="space-y-2">
                        <Inbox className="w-8 h-8 mx-auto opacity-50" />
                        <p className="text-sm">All tasks assigned!</p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="space-y-2">
                    {filteredTasks.map((task, index) => (
                      <TaskCard
                        key={task.id}
                        task={task}
                        index={index}
                        showFullDetails={true}
                        onTaskClick={openTaskDetails}
                      />
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Main Content - Pages */}
          <div className="lg:col-span-3 order-1 lg:order-2">
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-4 sm:gap-6">
              {state.pages.map((page) => (
                <div key={page.id} className="animate-fade-in">
                  <PageCard page={page} onTaskClick={openTaskDetails} />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      <AddPageModal
        isOpen={showAddPageModal}
        onClose={() => setShowAddPageModal(false)}
      />

      <AddTaskModal
        isOpen={showAddTaskModal}
        onClose={() => setShowAddTaskModal(false)}
      />

      {/* Simple Task Details Modal */}
      {showTaskDetails && selectedTask && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold mb-4">{selectedTask.title}</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Advanced features will be available once the database schemas are set up.
            </p>
            <div className="flex gap-2">
              <Button onClick={() => setShowTaskDetails(false)}>
                Close
              </Button>
              <Button variant="outline" onClick={() => window.open('/profile', '_blank')}>
                Setup Guide
              </Button>
            </div>
          </div>
        </div>
      )}
    </ModernLayout>
  );
};

export default Tasker;
