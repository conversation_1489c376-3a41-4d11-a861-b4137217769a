-- =====================================================
-- SETUP DATABASE FOR EASTASK
-- =====================================================
-- Run this in Supabase SQL Editor to set up all required tables

-- =====================================================
-- 1. CREATE NOTIFICATIONS TABLE
-- =====================================================
CREATE TABLE IF NOT EXISTS notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  workspace_id UUID REFERENCES workspaces(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN (
    'invitation', 
    'task_assignment', 
    'task_update', 
    'workspace_update', 
    'mention', 
    'system',
    'task_comment',
    'task_status_change',
    'workspace_invitation'
  )),
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  data JSONB DEFAULT '{}'::jsonb,
  read BOOLEAN DEFAULT FALSE,
  action_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =====================================================
-- 2. CREATE INDEXES
-- =====================================================
CREATE INDEX IF NOT EXISTS idx_notifications_user_id ON notifications(user_id);
CREATE INDEX IF NOT EXISTS idx_notifications_workspace_id ON notifications(workspace_id);
CREATE INDEX IF NOT EXISTS idx_notifications_created_at ON notifications(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_notifications_read ON notifications(read);
CREATE INDEX IF NOT EXISTS idx_notifications_user_read_created ON notifications(user_id, read, created_at DESC);

-- =====================================================
-- 3. ENABLE RLS
-- =====================================================
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- 4. CREATE RLS POLICIES
-- =====================================================
-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users can view their own notifications" ON notifications;
DROP POLICY IF EXISTS "Users can create notifications" ON notifications;
DROP POLICY IF EXISTS "Users can update their own notifications" ON notifications;
DROP POLICY IF EXISTS "Users can delete their own notifications" ON notifications;

-- Create new policies
CREATE POLICY "Users can view their own notifications" ON notifications
  FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can create notifications" ON notifications
  FOR INSERT WITH CHECK (true); -- Allow system to create notifications for any user

CREATE POLICY "Users can update their own notifications" ON notifications
  FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete their own notifications" ON notifications
  FOR DELETE USING (user_id = auth.uid());

-- =====================================================
-- 5. CREATE NOTIFICATION FUNCTIONS
-- =====================================================

-- Function to get unread notification count
CREATE OR REPLACE FUNCTION get_unread_notification_count(p_user_id UUID DEFAULT NULL)
RETURNS INTEGER AS $$
DECLARE
  target_user_id UUID;
  unread_count INTEGER;
BEGIN
  target_user_id := COALESCE(p_user_id, auth.uid());
  
  SELECT COUNT(*) INTO unread_count
  FROM notifications 
  WHERE user_id = target_user_id AND read = FALSE;
  
  RETURN unread_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark notification as read
CREATE OR REPLACE FUNCTION mark_notification_read(p_notification_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  UPDATE notifications 
  SET read = TRUE 
  WHERE id = p_notification_id AND user_id = auth.uid();
  
  RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to mark all notifications as read
CREATE OR REPLACE FUNCTION mark_all_notifications_read(p_user_id UUID DEFAULT NULL)
RETURNS INTEGER AS $$
DECLARE
  target_user_id UUID;
  updated_count INTEGER;
BEGIN
  target_user_id := COALESCE(p_user_id, auth.uid());
  
  UPDATE notifications 
  SET read = TRUE 
  WHERE user_id = target_user_id AND read = FALSE;
  
  GET DIAGNOSTICS updated_count = ROW_COUNT;
  RETURN updated_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =====================================================
-- 6. ENABLE REALTIME
-- =====================================================
ALTER PUBLICATION supabase_realtime ADD TABLE notifications;

-- =====================================================
-- 7. CREATE SAMPLE NOTIFICATION FOR TESTING
-- =====================================================
-- Only create if user is authenticated
DO $$
BEGIN
  IF auth.uid() IS NOT NULL THEN
    INSERT INTO notifications (
      user_id, 
      type, 
      title, 
      message, 
      data
    ) VALUES (
      auth.uid(),
      'system',
      'Welcome to EasTask!',
      'Your notification system is now set up and working perfectly.',
      '{"setup": true, "version": "1.0"}'::jsonb
    );
  END IF;
EXCEPTION WHEN OTHERS THEN
  -- Ignore errors if user not authenticated
  NULL;
END $$;

-- =====================================================
-- 8. VERIFICATION
-- =====================================================
-- Check if everything was created successfully
SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'notifications')
    THEN '✅ Notifications table created'
    ELSE '❌ Notifications table missing'
  END as table_status;

SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM pg_tables WHERE tablename = 'notifications' AND rowsecurity = true)
    THEN '✅ RLS enabled'
    ELSE '❌ RLS not enabled'
  END as rls_status;

SELECT 
  CASE 
    WHEN EXISTS (SELECT 1 FROM information_schema.routines WHERE routine_name = 'get_unread_notification_count')
    THEN '✅ Functions created'
    ELSE '❌ Functions missing'
  END as functions_status;

-- Show notification count for current user
SELECT 
  COALESCE(get_unread_notification_count(), 0) as unread_notifications,
  'for current user' as note;

-- Success message
SELECT '🎉 Database setup complete! Notifications system is ready.' as result;
