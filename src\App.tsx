

import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "./contexts/ThemeContext";
import { SupabaseAuthProvider } from "./contexts/SupabaseAuthContext";
import { SupabaseWorkspaceProvider } from "./contexts/SupabaseWorkspaceContext";
import { TaskProvider } from "./contexts/TaskContext";
import { NotificationProvider } from "./contexts/NotificationContext";
import { SearchProvider, useSearch } from "./contexts/SearchContext";
import ErrorBoundary from "./components/ErrorBoundary";
import AsyncErrorBoundary from "./components/AsyncErrorBoundary";
import ProtectedRoute from "./components/ProtectedRoute";
import CollaborationOverlay from "./components/CollaborationOverlay";
import Navbar from "./components/Navbar";
import GlobalSearchModal from "./components/GlobalSearchModal";
import SearchCommandPalette from "./components/SearchCommandPalette";
import Home from "./pages/Home";
import Tasker from "./pages/Tasker";
import AddPage from "./pages/AddPage";
import JoinWorkspace from "./pages/JoinWorkspace";
import WebsiteManager from "./components/WebsiteManager";
import Websites from "./pages/Websites";
import Team from "./pages/Team";
import Analytics from "./pages/Analytics";
import Calendar from "./pages/Calendar";
import Profile from "./pages/Profile";
import Settings from "./pages/Settings";
import FeatureDemo from "./pages/FeatureDemo";
import Landing from "./pages/Landing";
import ResetPassword from "./pages/ResetPassword";
import SupabaseTest from "./pages/SupabaseTest";
import TestTaskManagement from "./pages/TestTaskManagement";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

// AppContent component that uses search context
const AppContent = () => {
  const { isSearchOpen, closeSearch } = useSearch();

  return (
    <>
      <BrowserRouter>
        <div className="min-h-screen">
          <Routes>
            {/* Public routes */}
            <Route path="/login" element={<Landing />} />
            <Route path="/landing" element={<Landing />} />
            <Route path="/reset-password" element={<ResetPassword />} />
            <Route path="/join/:inviteCode" element={<JoinWorkspace />} />

            {/* Protected routes */}
            <Route path="/*" element={
              <ProtectedRoute>
                <CollaborationOverlay>
                  <Routes>
                    <Route path="/" element={<Home />} />
                    <Route path="/tasker" element={<Tasker />} />
                    <Route path="/websites" element={<Websites />} />
                    <Route path="/team" element={<Team />} />
                    <Route path="/analytics" element={<Analytics />} />
                    <Route path="/calendar" element={<Calendar />} />
                    <Route path="/profile" element={<Profile />} />
                    <Route path="/settings" element={<Settings />} />
                    <Route path="/features" element={<FeatureDemo />} />
                    <Route path="/add-page" element={<AddPage />} />
                    <Route path="/supabase-test" element={<SupabaseTest />} />
                    <Route path="/test-tasks" element={<TestTaskManagement />} />
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                </CollaborationOverlay>
              </ProtectedRoute>
            } />
          </Routes>
        </div>
      </BrowserRouter>

      {/* Global Search Modal */}
      <GlobalSearchModal
        isOpen={isSearchOpen}
        onClose={closeSearch}
        onResultClick={(result) => {
          console.log('Search result clicked:', result);
          closeSearch();
        }}
      />

      {/* Search Command Palette */}
      <SearchCommandPalette
        onNavigate={(path) => {
          console.log('Navigate to:', path);
        }}
        onCreateTask={() => {
          console.log('Create task');
        }}
        onCreatePage={() => {
          console.log('Create page');
        }}
        onOpenSettings={() => {
          console.log('Open settings');
        }}
      />
    </>
  );
};

const App = () => {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider>
          <TooltipProvider>
            <SupabaseAuthProvider>
              <NotificationProvider>
                <SupabaseWorkspaceProvider>
                  <AsyncErrorBoundary>
                    <TaskProvider>
                      <AsyncErrorBoundary>
                        <SearchProvider>
                          <Toaster />
                          <Sonner />
                          <AppContent />
                        </SearchProvider>
                      </AsyncErrorBoundary>
                    </TaskProvider>
                  </AsyncErrorBoundary>
                </SupabaseWorkspaceProvider>
              </NotificationProvider>
            </SupabaseAuthProvider>
          </TooltipProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;
